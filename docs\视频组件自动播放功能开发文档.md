# 视频组件自动播放功能开发文档

## 功能概述
为 dp-video 组件增加了完整的视频播放控制功能，包括自动播放、静音播放、循环播放和控制条显示等设置。

## 新增功能

### 1. 自动播放设置
- **参数**: `params.autoplay`
- **类型**: Boolean
- **默认值**: false
- **说明**: 开启后视频将自动播放，但可能被浏览器策略限制

### 2. 静音播放设置
- **参数**: `params.muted`
- **类型**: Boolean
- **默认值**: false
- **说明**: 开启后视频将静音播放，建议与自动播放一起使用

### 3. 循环播放设置
- **参数**: `params.loop`
- **类型**: Boolean
- **默认值**: false
- **说明**: 开启后视频播放结束时将自动重新开始

### 4. 控制条显示设置
- **参数**: `params.controls`
- **类型**: Boolean
- **默认值**: true
- **说明**: 控制是否显示视频播放控制条

## 修改的文件

### 1. DIY编辑页面
**文件**: `shangchengquan/shangcheng/app/home/<USER>/temp/edit-video.html`

**新增设置项**:
```html
<!-- 自动播放设置 -->
<div class="layui-form-item">
    <div class="layui-form-label">自动播放</div>
    <div class="layui-input-inline">
        <input class="checkbox" type="checkbox" lay-skin="switch" lay-text="开启|关闭" name="{{Edit.id}}_autoplay" ng-model="Edit.params.autoplay"/>
    </div>
    <div class="layui-form-mid layui-word-aux">开启后视频将自动播放（部分浏览器可能限制自动播放）</div>
</div>

<!-- 静音播放设置 -->
<div class="layui-form-item">
    <div class="layui-form-label">静音播放</div>
    <div class="layui-input-inline">
        <input class="checkbox" type="checkbox" lay-skin="switch" lay-text="开启|关闭" name="{{Edit.id}}_muted" ng-model="Edit.params.muted"/>
    </div>
    <div class="layui-form-mid layui-word-aux">开启后视频将静音播放（建议与自动播放一起使用）</div>
</div>

<!-- 循环播放设置 -->
<div class="layui-form-item">
    <div class="layui-form-label">循环播放</div>
    <div class="layui-input-inline">
        <input class="checkbox" type="checkbox" lay-skin="switch" lay-text="开启|关闭" name="{{Edit.id}}_loop" ng-model="Edit.params.loop"/>
    </div>
    <div class="layui-form-mid layui-word-aux">开启后视频将循环播放</div>
</div>

<!-- 控制条显示设置 -->
<div class="layui-form-item">
    <div class="layui-form-label">显示控制条</div>
    <div class="layui-input-inline">
        <input class="checkbox" type="checkbox" lay-skin="switch" lay-text="显示|隐藏" name="{{Edit.id}}_controls" ng-model="Edit.params.controls" ng-init="Edit.params.controls = Edit.params.controls !== false"/>
    </div>
    <div class="layui-form-mid layui-word-aux">控制是否显示视频播放控制条</div>
</div>
```

### 2. 前端视频组件
**文件**: `tiantianshande/components/dp-video/dp-video.vue`

**主要修改**:

#### 模板部分
```vue
<video
    :controls="params.controls !== false"
    :autoplay="params.autoplay && !showPoster"
    :muted="params.muted"
    :loop="params.loop"
    ...
>
```

#### 计算属性
```javascript
computed: {
    showPoster() {
        // 智能控制封面显示逻辑
        if (!this.params.pic) return false
        if (this.params.autoplay) return false
        if (this.isPlaying) return false
        return true
    }
}
```

#### 新增方法
```javascript
tryAutoPlay() {
    // 尝试自动播放视频，处理浏览器限制
    if (this.params.autoplay && this.params.src) {
        try {
            const videoContext = uni.createVideoContext('myVideo', this)
            if (videoContext) {
                videoContext.play()
                this.isPlaying = true
            }
        } catch (error) {
            console.log('自动播放失败，可能被浏览器阻止:', error)
            this.isPlaying = false
        }
    }
}
```

## 功能特性

### 1. 智能封面控制
- 开启自动播放时，自动隐藏封面图片
- 自动播放失败时，显示封面供用户手动点击
- 保持原有的手动播放体验

### 2. 浏览器兼容性处理
- 处理浏览器自动播放策略限制
- 提供静音播放选项提高自动播放成功率
- 自动播放失败时优雅降级

### 3. 循环播放优化
- 视频结束时根据循环设置决定是否重置播放状态
- 与自动播放配合实现无缝循环

### 4. 控制条灵活配置
- 默认显示控制条，保持用户体验
- 支持隐藏控制条实现沉浸式播放

## 使用示例

### 基础自动播放
```vue
<dp-video :params="{
    src: 'video.mp4',
    autoplay: true,
    muted: true
}" />
```

### 循环静音播放
```vue
<dp-video :params="{
    src: 'video.mp4',
    autoplay: true,
    muted: true,
    loop: true,
    controls: false
}" />
```

### 手动播放（默认）
```vue
<dp-video :params="{
    src: 'video.mp4',
    pic: 'poster.jpg',
    controls: true
}" />
```

## 注意事项

1. **浏览器限制**: 大多数现代浏览器限制自动播放，建议配合静音使用
2. **用户体验**: 自动播放应谨慎使用，避免影响用户体验
3. **性能考虑**: 自动播放会增加流量消耗，移动端需特别注意
4. **兼容性**: 不同平台对视频属性支持可能有差异

## 测试建议

1. 测试不同浏览器的自动播放行为
2. 验证静音播放的效果
3. 测试循环播放的流畅性
4. 检查控制条显示/隐藏功能
5. 验证封面图片与自动播放的交互逻辑
