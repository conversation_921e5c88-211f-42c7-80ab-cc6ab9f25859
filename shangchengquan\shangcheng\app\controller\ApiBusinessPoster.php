<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 商家海报API接口 - 专门用于商家后台
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\Db;
use think\facade\Cache;

class ApiBusinessPoster extends ApiAdmin
{
    /**
     * 生成商家海报
     */
    public function poster(){
        $posterid = input('param.posterid');
        $business_id = bid; // 使用当前登录商家的ID
        
        if(!$business_id){
            echojson(['status'=>0,'msg'=>'请先登录商家账号']);
        }
        
        $platform = platform;
        $page = '/pagesExt/business/index?id='.$business_id;
        
        // 检查是否开启商家二维码注册绑定功能
        $business_sysset = Db::name('business_sysset')->where('aid',aid)->find();
        $business = Db::name('business')->where('aid',aid)->where('id',$business_id)->find();
        
        if($business_sysset['business_qr_register_bind_enabled'] == 1 && $business && $business['mid'] > 0){
            // 开启绑定且商家已绑定用户，使用pid场景
            $scene = 'pid_'.$business['mid'];
            \think\facade\Log::write('商家二维码启用注册绑定功能，scene='.$scene.'，商家ID='.$business_id.'，绑定用户ID='.$business['mid']);
        } else {
            // 使用默认的商家场景
            $scene = 'bid_'.$business_id;
        }

        if($posterid){
            $posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','business')->where('platform',$platform)->where('id',$posterid)->find();
        }else{
            $posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','business')->where('platform',$platform)->order('id')->find();
        }
        
        // 如果海报模板不存在，创建默认模板
        if(!$posterset){
            // 为mp、h5等平台创建默认模板（使用qrmp类型二维码）
            $data_business_mp = jsonEncode([
                'poster_bg' => PRE_URL.'/static/imgsrc/posterbg.jpg',
                'poster_data' => [
                    ['left' => '221px','top' => '446px','type' => 'qrmp','width' => '94px','height' => '94px','size' => ''],
                    ['left' => '30px','top' => '70px','type' => 'img','width' => '285px','height' => '285px','src' => '/static/imgsrc/business_default.jpg'],
                    ['left' => '30px','top' => '370px','type' => 'textarea','width' => '286px','height' => '47px','size' => '16px','color' => '#000','content' => '[商家名称]','center' => '0'],
                    ['left' => '34px','top' => '452px','type' => 'head','width' => '47px','height' => '47px','radius' => '100'],
                    ['left' => '89px','top' => '459px','type' => 'text','width' => '50px','height' => '18px','size' => '16px','color' => '#333333','content' => '[昵称]','center' => '0'],
                    ['left' => '90px','top' => '484px','type' => 'text','width' => '98px','height' => '14px','size' => '12px','color' => '#B6B6B6','content' => '推荐给你一个商家','center' => '0'],
                    ['left' => '35px','top' => '516px','type' => 'text','width' => '142px','height' => '22px','size' => '20px','color' => '#FD0000','content' => '[商家描述]','center' => '0'],
                    ['left' => '35px','top' => '540px','type' => 'text','width' => '280px','height' => '16px','size' => '14px','color' => '#BBBBBB','content' => '地址:[商家地址]','center' => '0']
                ]
            ]);
            
            // 为wx平台创建默认模板（使用qrwx类型二维码）
            $data_business_wx = jsonEncode([
                'poster_bg' => PRE_URL.'/static/imgsrc/posterbg.jpg',
                'poster_data' => [
                    ['left' => '221px','top' => '446px','type' => 'qrwx','width' => '94px','height' => '94px','size' => ''],
                    ['left' => '30px','top' => '70px','type' => 'img','width' => '285px','height' => '285px','src' => '/static/imgsrc/business_default.jpg'],
                    ['left' => '30px','top' => '370px','type' => 'textarea','width' => '286px','height' => '47px','size' => '16px','color' => '#000','content' => '[商家名称]','center' => '0'],
                    ['left' => '34px','top' => '452px','type' => 'head','width' => '47px','height' => '47px','radius' => '100'],
                    ['left' => '89px','top' => '459px','type' => 'text','width' => '50px','height' => '18px','size' => '16px','color' => '#333333','content' => '[昵称]','center' => '0'],
                    ['left' => '90px','top' => '484px','type' => 'text','width' => '98px','height' => '14px','size' => '12px','color' => '#B6B6B6','content' => '推荐给你一个商家','center' => '0'],
                    ['left' => '35px','top' => '516px','type' => 'text','width' => '142px','height' => '22px','size' => '20px','color' => '#FD0000','content' => '[商家描述]','center' => '0'],
                    ['left' => '35px','top' => '540px','type' => 'text','width' => '280px','height' => '16px','size' => '14px','color' => '#BBBBBB','content' => '地址:[商家地址]','center' => '0']
                ]
            ]);
            
            // 为各平台插入默认模板数据
            Db::name('admin_set_poster')->insert(['aid'=>aid,'type'=>'business','platform'=>'mp','content'=>$data_business_mp]);
            Db::name('admin_set_poster')->insert(['aid'=>aid,'type'=>'business','platform'=>'wx','content'=>$data_business_wx]);
            Db::name('admin_set_poster')->insert(['aid'=>aid,'type'=>'business','platform'=>'alipay','content'=>$data_business_mp]);
            Db::name('admin_set_poster')->insert(['aid'=>aid,'type'=>'business','platform'=>'baidu','content'=>$data_business_mp]);
            Db::name('admin_set_poster')->insert(['aid'=>aid,'type'=>'business','platform'=>'toutiao','content'=>$data_business_mp]);
            Db::name('admin_set_poster')->insert(['aid'=>aid,'type'=>'business','platform'=>'qq','content'=>$data_business_mp]);
            Db::name('admin_set_poster')->insert(['aid'=>aid,'type'=>'business','platform'=>'h5','content'=>$data_business_mp]);
            Db::name('admin_set_poster')->insert(['aid'=>aid,'type'=>'business','platform'=>'app','content'=>$data_business_mp]);

            // 重新查询当前平台的海报模板
            $posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','business')->where('platform',$platform)->order('id')->find();
        }
        
        if(!$posterset){
            echojson(['status'=>0,'msg'=>'商家海报模板初始化失败']);
        }
        
        $posterdata = Db::name('business_poster')->where('aid',aid)->where(['bid'=>$business_id,'posterid'=>$posterset['id']])->find();
        $business = Db::name('business')->where('aid',aid)->where('id',$business_id)->find();
        
        if(!$business){
            echojson(['status'=>0,'msg'=>'商家信息不存在']);
        }
        
        if(!$posterdata){
            $textReplaceArr = [
                '[商家名称]'=>$business['name'],
                '[商家描述]'=>$business['desc'],
                '[商家地址]'=>$business['address'],
                '[联系电话]'=>$business['linktel'] ?: $business['tel'],
            ];
            $poster = $this->_getposter(aid,$platform,$posterset['content'],$page,$scene,$textReplaceArr);
            $posterdata = [];
            $posterdata['aid'] = aid;
            $posterdata['bid'] = $business_id;
            $posterdata['scene'] = $scene;
            $posterdata['page'] = $page;
            $posterdata['poster'] = $poster;
            $posterdata['posterid'] = $posterset['id'];
            $posterdata['createtime'] = time();
            Db::name('business_poster')->insert($posterdata);
        }

        $posterlist = Db::name('admin_set_poster')->field('id')->where('aid',aid)->where('type','business')->where('platform',$platform)->order('id')->select()->toArray();

        $rdata = [];
        $rdata['poster'] = $posterdata['poster'];
        $rdata['guize'] = $posterset['guize'];
        $rdata['posterid'] = $posterset['id'];
        $rdata['posterlist'] = $posterlist;
        $rdata['postercount'] = count($posterlist);
        echojson(['status'=>1,'data'=>$rdata]);
    }
    
    /**
     * 生成网页付款二维码
     */
    public function getWebPayQr(){
        $business_id = bid;

        // 添加调试日志
        \think\facade\Log::info("getWebPayQr - 原始bid常量值: " . var_export(bid, true));
        \think\facade\Log::info("getWebPayQr - business_id变量值: " . var_export($business_id, true));

        if(!$business_id){
            echojson(['status'=>0,'msg'=>'请先登录商家账号']);
        }

        // 确保business_id是数字
        $business_id = intval($business_id);
        \think\facade\Log::info("getWebPayQr - 转换后的business_id: " . $business_id);

        // 检查是否开启商家二维码注册绑定功能
        $business_sysset = Db::name('business_sysset')->where('aid',aid)->find();
        $business = Db::name('business')->where('aid',aid)->where('id',$business_id)->find();

        if(!$business){
            \think\facade\Log::info("getWebPayQr - 商家不存在，business_id: " . $business_id);
            echojson(['status'=>0,'msg'=>'商家不存在']);
        }

        if($business_sysset['business_qr_register_bind_enabled'] == 1 && $business && $business['mid'] > 0){
            // 开启绑定且商家已绑定用户，使用pid场景
            $scene = 'pid_'.$business['mid'];
            \think\facade\Log::info("getWebPayQr - 使用pid场景: " . $scene . ", 商家绑定用户ID: " . $business['mid']);
        } else {
            // 使用默认的商家场景
            $scene = 'bid_'.$business_id;
            \think\facade\Log::info("getWebPayQr - 使用bid场景: " . $scene);
        }

        // 网页付款链接 - 确保bid参数使用商家ID，scene参数使用正确场景
        $payUrl = PRE_URL . '/h5/'.aid.'.html#/pagesExt/maidan/pay?bid=' . $business_id . '&scene=' . $scene . '&t=' . time();

        \think\facade\Log::info("getWebPayQr - 最终生成的URL: " . $payUrl);

        // 生成二维码
        $qrcode = $this->generateQrCode($payUrl, 'web_pay_'.$business_id);

        echojson(['status'=>1,'data'=>['qr_url'=>$qrcode,'pay_url'=>$payUrl]]);
    }
    
    /**
     * 生成小程序付款二维码
     */
    public function getMiniPayQr(){
        $business_id = bid;
        if(!$business_id){
            echojson(['status'=>0,'msg'=>'请先登录商家账号']);
        }
        
        $platform = platform;
        $page = '/pagesExt/maidan/pay';
        
        // 检查是否开启商家二维码注册绑定功能
        $business_sysset = Db::name('business_sysset')->where('aid',aid)->find();
        $business = Db::name('business')->where('aid',aid)->where('id',$business_id)->find();
        
        if($business_sysset['business_qr_register_bind_enabled'] == 1 && $business && $business['mid'] > 0){
            // 开启绑定且商家已绑定用户，使用pid场景
            $scene = 'pid_'.$business['mid'];
        } else {
            // 使用默认的商家场景
            $scene = 'bid_'.$business_id;
        }
        
        if($platform == 'wx'){
            // 生成微信小程序码
            $qrcode = $this->getWxacode($page.'?bid='.$business_id, $scene);
            if(!$qrcode){
                echojson(['status'=>0,'msg'=>'小程序码生成失败，请检查小程序配置或页面是否存在']);
                return;
            }
        } else {
            // 其他平台生成普通二维码
            $payUrl = '/pagesExt/maidan/pay?bid=' . $business_id;
            $qrcode = $this->generateQrCode($payUrl, 'mini_pay_'.$business_id);
        }
        
        echojson(['status'=>1,'data'=>['qr_url'=>$qrcode,'pay_path'=>$page.'?bid='.$business_id]]);
    }
    
    /**
     * 获取商家海报列表
     */
    public function getPosterList(){
        $business_id = bid;
        $platform = platform;
        
        $posterList = Db::name('admin_set_poster')
            ->field('id,content,guize,createtime')
            ->where('aid', aid)
            ->where('type', 'business')
            ->where('platform', $platform)
            ->order('id desc')
            ->select()
            ->toArray();
            
        foreach($posterList as &$poster){
            $poster['content'] = json_decode($poster['content'], true);
        }
        
        echojson(['status'=>1,'data'=>$posterList]);
    }
    
    /**
     * 清除海报缓存
     */
    public function clearPosterCache(){
        $posterid = input('posterid', 0);
        $business_id = bid;
        
        if($posterid){
            // 删除数据库记录
            Db::name('business_poster')
                ->where('aid', aid)
                ->where('bid', $business_id)
                ->where('posterid', $posterid)
                ->delete();
        } else {
            // 清除所有海报缓存
            Db::name('business_poster')
                ->where('aid', aid)
                ->where('bid', $business_id)
                ->delete();
        }
        
        echojson(['status'=>1,'msg'=>'缓存清除成功']);
    }
    
    /**
     * 生成二维码
     */
    private function generateQrCode($content, $filename){
        // 这里应该使用实际的二维码生成库
        // 暂时返回模拟URL
        $uploadDir = ROOT_PATH . 'public/uploads/qrcode/';
        if(!is_dir($uploadDir)){
            mkdir($uploadDir, 0755, true);
        }
        
        $qrFile = $filename . '_' . time() . '.png';
        $qrPath = $uploadDir . $qrFile;
        
        // TODO: 这里应该调用实际的二维码生成方法
        // 使用 QrCode 库生成二维码
        // 暂时返回模拟路径
        $qrUrl = PRE_URL . '/uploads/qrcode/' . $qrFile;
        
        return $qrUrl;
    }
    
    /**
     * 获取微信小程序码
     */
    private function getWxacode($path, $scene){
       // \app\common\System::plog('商家海报生成微信小程序码');
        
        // 记录生成小程序码的参数
        \think\facade\Log::write('商家海报生成微信小程序码参数：'.jsonEncode([
            'aid' => aid,
            'path' => $path,
            'scene' => $scene,
            'platform' => 'wx'
        ]));
        
        // 使用系统通用的小程序码生成方法
        $result = \app\common\Wechat::getQRCode(aid, 'wx', $path, ['scene' => $scene]);
        
        if($result['status'] == 1){
            \think\facade\Log::write('商家海报微信小程序码生成成功：'.$result['url']);
            return $result['url'];
        } else {
            \think\facade\Log::write('商家海报微信小程序码生成失败：'.jsonEncode($result));
            // 如果生成失败，返回空字符串，让调用方处理
            return '';
        }
    }
} 