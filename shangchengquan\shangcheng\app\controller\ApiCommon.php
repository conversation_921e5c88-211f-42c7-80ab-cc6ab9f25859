<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 公共接口
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\Db;
class ApiCommon extends ApiBase
{
	public $sysset;
    public function initialize(){
		parent::initialize();
		if(input('param.pid')){
			$fromid = input('param.pid/d');
		}
		$tuanzhangid = 0;
		if(input('param.tuanzhangid'))
		{
		    $tuanzhangid = input('param.tuanzhangid/d');
		}
		
		

		//后台查看h5管理端
		$uid = input('param.uid/d');
		if($uid/* && session("?ADMIN_LOGIN") && (session('ADMIN_UID') == $uid || session('ADMIN_AUTH_UID') == $uid)*/){
			$user = Db::name('admin_user')->where('id',$uid)->find();
			if(!$user) die(jsonEncode(['status'=>0,'msg'=>'管理员不存在']));
			$member = Db::name('member')->where('aid',aid)->where('id',$user['mid'])->find();
			if(!$member) die(jsonEncode(['status'=>0,'msg'=>'管理员绑定的会员不存在']));
			$mid = $member['id'];
			$this->sessionid = \think\facade\Session::getId().$uid;
			cache($this->sessionid.'_mid',$mid,3600);
			Db::name('session')->where('aid',aid)->where('session_id',$this->sessionid)->delete();
			$session = [
				'session_id' => $this->sessionid,
				'aid' => aid,
				'mid' => $mid,
				'user_agent' => $_SERVER['HTTP_USER_AGENT'],
				'login_time' => time(),
				'login_ip' => request()->ip(),
				'platform' => platform
			];
			Db::name('session')->insert($session);
		}else{
			if(input('param.session_id') && input('param.session_id') != 'undefined' && input('param.session_id') != 'null' && !empty(input('param.session_id'))){
				$session = Db::name('session')->where('aid',aid)->where('session_id',input('param.session_id'))->find();
				if(empty($session)) {
					$this->sessionid = input('param.session_id');//\think\facade\Session::getId();
					$mid = cache($this->sessionid.'_mid');
					$session = [
						'session_id' => $this->sessionid,
						'aid' => aid,
						'mid' => $mid,
						'user_agent' => $_SERVER['HTTP_USER_AGENT'],
						'login_time' => time(),
						'login_ip' => request()->ip(),
						'platform' => platform
					];
					Db::name('session')->insert($session);
				} else {
					$this->sessionid = input('param.session_id');
				}
			}else{
				$this->sessionid = \think\facade\Session::getId();
				$mid = cache($this->sessionid.'_mid');
				$session = [
					'session_id' => $this->sessionid,
					'aid' => aid,
					'mid' => $mid,
					'user_agent' => $_SERVER['HTTP_USER_AGENT'],
					'login_time' => time(),
					'login_ip' => request()->ip(),
					'platform' => platform
				];
				Db::name('session')->insert($session);
			}
			$mid = cache($this->sessionid.'_mid');

			$member = [];
			if($mid){
				$member = Db::name('member')->where('aid',aid)->where('id',$mid)->find();
				//$session = Db::name('session')->where('aid',aid)->where('mid',$mid)->order('login_time desc')->find();
				//if($member && $session['session_id']) $this->sessionid = $session['session_id'];
			}elseif($this->sessionid){
				//$session = Db::name('session')->where('aid',aid)->where('session_id',$this->sessionid)->order('login_time desc')->find();
				//if($session['mid']) $member = Db::name('member')->where('aid',aid)->where('id',$session['mid'])->find();
			}
		}
		if(!$member){
			define('mid',0);
		}else{
			define('mid',$member['id']);
			if($fromid && $fromid!=mid && $fromid!=$member['pid']){
				$upuser = Db::name('member')->where('id',$fromid)->find();
				$uplv = Db::name('member_level')->where('aid',aid)->where('id',$upuser['levelid'])->find();
				if($upuser && $uplv['can_agent']!=0 && $uplv['agent_rule']>0){
					if(!$member['pid'] || $uplv['agent_rule']==2){
						Db::name('member')->where('id',mid)->update(['pid'=>$fromid]);
						\app\common\Common::user_tjscore(aid,mid);
					}elseif($uplv['agent_rule']==3){ //首次消费后绑定推荐关系
						$haspayorder = Db::name('payorder')->where('mid',mid)->where('money','>',0)->where('status',1)->find();
						if(!$haspayorder){
							Db::name('member')->where('id',mid)->update(['pid'=>$fromid]);
							//\app\common\Common::user_tjscore(aid,mid);
						}
					}
				}
			}
            $member['money'] = \app\common\Member::getmoney($member);
            $member['score'] = \app\common\Member::getscore($member);
		}
		$this->mid = mid;
		$this->member = $member;
		if($this->sessionid && !$mid){
			cache($this->sessionid.'_mid',mid,3600);
		}
		
		$this->sysset = Db::name('admin_set')->where('aid',aid)->find();
		$gettj = explode(',',$this->sysset['gettj']);
		if(!in_array('-1',$gettj)){ //不是所有人
			$request_action = request()->controller().'/'.request()->action();
			if($request_action=='ApiIndex/index' || request()->controller()!='ApiIndex'){
				$this->checklogin();
				if(!in_array($this->member['levelid'],$gettj)){
					echojson(['status'=>-4,'msg'=>$this->sysset['gettjtip']]);
				}
			}
		}
		//设置了进入系统必须先登录
		if($this->sysset['login_mast'] && in_array(platform,explode(',',$this->sysset['login_mast']))){
			$request_action = request()->controller().'/'.request()->action();
			if($request_action=='ApiIndex/index' || !in_array(request()->controller(),['ApiIndex','ApiImageupload'])){
				$this->checklogin();
			}
		}
		
		
		$request_action = request()->controller().'/'.request()->action();
		   // 从认证中心设置表中获取开启的认证项目
        $renzhengItems = Db::name('renzhengzhongxinset')->where('aid', aid)->find();

// 		if($this->sysset['idcard_check'] == 1 && request()->controller()!='ApiIndex' && $request_action != 'ApiMy/setfield')
// 		{
// 		    $this->checklogin();
// 		    $usercard = Db::name('member')->where('id',$this->mid)->value('usercard');
//             if(!$usercard)
//             {
//               echojson(['status'=>-4,'msg'=>'请去认证','url'=>'/pages/my/setidcard']);
//             }
// 		}
// 		if($this->sysset['idcard_check'] == 1 && $request_action=='ApiIndex/index')
// 		{
// 		    $this->checklogin();
// 	        $usercard = Db::name('member')->where('id',$this->mid)->value('usercard');
// 	        if(!$usercard)
//             {
//                 echojson(['status'=>-4,'msg'=>'请去认证','url'=>'/pages/my/setidcard']);
//             }
// 		}
        	
        // 检查总认证开关是否开启
        if ($renzhengItems && isset($renzhengItems['status']) && $renzhengItems['status'] == 1) {
        
     // 定义需要排除的请求动作数组
$excluded_actions = [
    'ApiMy/getRenzhengItems',
    'ApiMy/set',
    'ApiImageupload/uploadImg',
    'ApiMiaosha/setfield',
    'ApiMy/setfield',
    'ApiFace/info',
    'ApiFace/detect',
    'ApiFace/record',
    'ApiFace/query',
    
    // 如果有其他需要排除的请求动作，可以在这里添加
    // 'ApiMy/anotherAction',
    // 'ApiIndex/someAction',
];

// 人脸认证开关

if (
    $renzhengItems['face_verification'] == 1 &&
    $renzhengItems['status'] == 1 &&
    request()->controller() != 'ApiIndex' &&
    !in_array($request_action, $excluded_actions)
) {
    $this->checklogin();
    $face_verified = Db::name('member')->where('id', $this->mid)->value('face_verified');
    if (!$face_verified) {
        echojson(['status' => -4, 'msg' => '请进行人脸认证', 'url' => '/pagesExa/my/renzhengzhongxin']);
    }
}

// 资料填写认证开关

// if (
//     $renzhengItems['fill_info'] == 1 &&
//     $renzhengItems['status'] == 1 &&
//     request()->controller() != 'ApiIndex' &&
//     !in_array($request_action, $excluded_actions)
// ) {
//     $this->checklogin();
//     $name = Db::name('member')->where('id', $this->mid)->value('realname');
//     $mobile = Db::name('member')->where('id', $this->mid)->value('tel');
//     if (empty($name) || empty($mobile)) {
//         echojson(['status' => -4, 'msg' => '请先完善资料', 'url' => '/pagesExa/my/renzhengzhongxin']);
//     }
// }


// 收货地址完善开关

// if (
//     $renzhengItems['address_completion'] == 1 &&
//     $renzhengItems['status'] == 1 &&
//     request()->controller() != 'ApiIndex' &&
//     !in_array($request_action, $excluded_actions)
// ) {
//     $this->checklogin();
//     $address = Db::name('member_address')->where('aid', aid)->where('mid', $this->mid)->find();
//     if (empty($address)) {
//         echojson(['status' => -4, 'msg' => '请完善收货地址', 'url' => '/pagesExa/my/renzhengzhongxin']);
//     }
// }


// 银行卡完善开关

if (
    $renzhengItems['bankcard_completion'] == 1 &&
    $renzhengItems['status'] == 1 &&
    request()->controller() != 'ApiIndex' &&
    !in_array($request_action, $excluded_actions)
) {
    $this->checklogin();
    $bankcard_info = Db::name('member')->where('id', $this->mid)->value('bankcard_info');
    if (empty($bankcard_info)) {
        echojson(['status' => -4, 'msg' => '请完善银行卡信息', 'url' => '/pagesExa/my/renzhengzhongxin']);
    }
}


// 支付宝收款码完善开关
if (
    $renzhengItems['alipay_code'] == 1 &&
    $renzhengItems['status'] == 1 &&
    request()->controller() != 'ApiIndex' &&
    !in_array($request_action, $excluded_actions)
)
{
    $this->checklogin();
    $alipay_code = Db::name('member')->where('id', $this->mid)->value('zfbimg');
    if (empty($alipay_code)) {
       //  echojson(['status' => -4, 'msg' => '请上传支付宝收款码', 'url' => '/pagesExa/my/renzhengzhongxin']);
    }
}

// 微信收款码完善开关
if (
    $renzhengItems['wechat_code'] == 1 &&
    $renzhengItems['status'] == 1 &&
    request()->controller() != 'ApiIndex' &&
    !in_array($request_action, $excluded_actions)
) {
    $this->checklogin();
    $wechat_code = Db::name('member')->where('id', $this->mid)->value('wximg');
    if (empty($wechat_code)) {
        echojson(['status' => -4, 'msg' => '请上传微信收款码', 'url' => '/pagesExa/my/renzhengzhongxin']);
    }
}

// 协议签署/电子签名开关
if (
    $renzhengItems['agreement_signing'] == 1 &&
    $renzhengItems['status'] == 1 &&
    request()->controller() != 'ApiIndex' &&
    !in_array($request_action, $excluded_actions)
) {
    $this->checklogin();
    $agreement_signed = Db::name('member')->where('id', $this->mid)->value('agreement_signed');
    if (!$agreement_signed) {
        // echojson(['status' => -4, 'msg' => '请签署协议', 'url' => '/pagesExa/my/renzhengzhongxin']);
    }
}

            
            
        }
		if($tuanzhangid >0 && !empty($member))
		{
		    if($member['pid'] == 0)
		    {
		        $tunzhang = Db::name('tuanzhang')->where('aid',aid)->where('id',$tuanzhangid)->field('mid2')->find();
		        if($tunzhang['mid2'] >0)
		        {
		            $tuozhang_member = Db::name('member')->field('levelid')->where('aid',aid)->where('id',$tunzhang['mid2'])->find();
		            
		            if(!empty($tuozhang_member) && $tuozhang_member['levelid'] >0)
		            {
		                $tuozhang_member_level = Db::name('member_level')->field('can_agent')->where('aid',aid)->where('id',$tuozhang_member['levelid'])->find();
		                if($tuozhang_member_level['can_agent'] >0){
		                     Db::name('member')->where('id',mid)->update(['pid'=>$tunzhang['mid2']]);
		                     \app\common\Common::user_tjscore(aid,mid);
		                }
		            }
		        }
		    }
		}
	}
	//判断登录 - 增强版支持静默登录
	public function checklogin($authlogin = 0, $params = []){
		if(!$this->member){
			//是否直接用授权登录
			$logintype = $this->sysset['logintype_'.platform];
			if($logintype == 3){
				$xieyi = Db::name('admin_set_xieyi')->where('aid',aid)->find();
				if(!$xieyi || $xieyi['status'] == 0){
					$authlogin = 1;
				}
			}
			
							// 检查是否启用自动注册登录功能
		\think\facade\Log::write('ApiCommon::checklogin 尝试检查member_auto_addlogin字段，平台: '.platform);
		\think\facade\Log::write('ApiCommon::checklogin 当前请求URI: '.$_SERVER['REQUEST_URI']);
		
		if($this->sysset['member_auto_addlogin'] == 1 && in_array(platform, ['wx', 'alipay', 'mp'])){
			// 对于支持静默登录的平台，检查登录类型配置
			$logintype = $this->sysset['logintype_'.platform];
			if(!$logintype){ 
				$logintype = [];
			}else{
				$logintype = explode(',',$logintype);
			}
			
			\think\facade\Log::write('ApiCommon::checklogin 平台登录类型配置: '.json_encode($logintype));
			
			// 优化逻辑：优先检查是否支持授权登录(3)，如果支持则让前端先尝试静默授权
			$supportsAuth = in_array('3', $logintype);
			$isInitRequest = strpos($_SERVER['REQUEST_URI'], 'needinit=1') !== false;
			
			\think\facade\Log::write('ApiCommon::checklogin 支持授权登录: '.($supportsAuth ? 'true' : 'false').', 初始化请求: '.($isInitRequest ? 'true' : 'false'));
			
			// 如果是初始化请求且支持授权登录，优先让前端处理静默授权
			if($isInitRequest && $supportsAuth){
				\think\facade\Log::write('ApiCommon::checklogin 检测到初始化请求且支持授权登录，优先让前端处理静默授权');
				return $this->json(['status'=>-1,'msg'=>'请先登录','authlogin'=>$authlogin,'data'=>$params],1);
			}
			
			// 如果logintype为空（纯静默登录模式），也让前端处理
			if(empty($logintype)){
				\think\facade\Log::write('ApiCommon::checklogin 检测到纯静默登录配置，让前端处理');
				return $this->json(['status'=>-1,'msg'=>'请先登录','authlogin'=>$authlogin,'data'=>$params],1);
			}
			
			// 其他情况才执行自动注册（非初始化请求或不支持授权登录）
			\think\facade\Log::write('ApiCommon::checklogin 执行自动注册逻辑');
			$member = \app\common\Member::autoReg(aid, $this->sessionid, platform);
			if($member){
				$this->member = $member;
				define('mid', $member['id']);
				$this->mid = $member['id'];
				// 更新访问时间
				Db::name('member')->where('aid',aid)->where('id',mid)->update(['last_visittime'=>time()]);
				return; // 自动登录成功，直接返回
			}
		}
			
			return $this->json(['status'=>-1,'msg'=>'请先登录','authlogin'=>$authlogin,'data'=>$params],1);
		}elseif($this->member['checkst'] == 0){
			echojson(['status'=>-4,'msg'=>'账号审核中']);
		}elseif($this->member['checkst'] == 2){
			echojson(['status'=>-4,'msg'=>'账号审核未通过,驳回原因：'.$this->member['checkreason']]);
		}elseif($this->member['isfreeze'] == 1){
			echojson(['status'=>-4,'msg'=>'账号已冻结']);
		}
        //更新访问时间
        Db::name('member')->where('aid',aid)->where('id',mid)->update(['last_visittime'=>time()]);
	}
	protected function json($data,$isexit=0){
		if(input('param.needinit') == 1){
			$platform = platform;
			$menuset = Db::name('designer_menu')->where('aid',aid)->where('platform',$platform)->find();
			$menudata = json_decode($menuset['menudata'],true);
			$menulist = array();
			foreach($menudata['list'] as $k=>$v){
				if($k < $menuset['menucount']){
					$menulist[] = $v;
				}
			}
			$menudata['list'] = $menulist;
			$indexurl = $menuset['indexurl'];
			
			$menu2datalist = [];
			//多商户默认导航
			$menubusinessset = Db::name('designer_menu_business')->where('aid',aid)->where('platform',$platform)->find();
			if($menubusinessset){
				$menubusinessdata = json_decode($menubusinessset['menudata'],true);
				$menubusinesslist = array();
				foreach($menubusinessdata['list'] as $k=>$v){
					if($k < $menubusinessset['menucount']){
						$menu2data = [];
						$menu2data['color'] = $menubusinessdata['color'];
						$menu2data['selectedColor'] = $menubusinessdata['selectedColor'];
						$menu2data['backgroundColor'] = $menubusinessdata['backgroundColor'];
						$menu2data['indexurl'] = $v['pagePath'];
						$menu2data['list'] = $menubusinessdata['list'];
						$menulist = [];
						foreach($menu2data['list'] as $k2=>$v2){
							if($k2 < $menubusinessset['menucount']){
								$menulist[] = $v2;
							}
						}
						$menu2data['list'] = $menulist;
						$menu2datalist[] = $menu2data;
					}
				}
			}

			//内页菜单
			$menu2list = Db::name('designer_menu2')->where('aid',aid)->where('status',1)->where('platform','in',['all',$platform])->order('id desc')->select()->toArray();
			if($menu2list){
				foreach($menu2list as $k=>$v){
					$menu2data = [];
					$menu2data['backgroundColor'] = $v['backgroundColor'];
					$menu2data['indexurl'] = $v['indexurl'];
					$menu2data['list'] = json_decode($v['menudata'],true);
					$menulist = [];
					foreach($menu2data['list'] as $k2=>$v2){
						if($k2 < $v['menucount']){
							$menulist[] = $v2;
						}
					}
					$menu2data['list'] = $menulist;
					$menu2datalist[] = $menu2data;
				}
			}


			$sysset = $this->sysset;
			$textset = json_decode($sysset['textset'],true);
			if(!$textset) $textset = [];

			//分享设置
			$sharelist = Db::name('designer_share')->field('title,desc,pic,url,indexurl,is_rootpath')->where('aid',aid)->where('status',1)->where('platform','in',['all',$platform])->select()->toArray();
			if(!$sharelist) $sharelist = [];

			if($platform == 'wx'){
				if($sysset['wxkf'] == 1){
					$sysset['kfurl'] = 'contact::';
				}else{
					$sysset['kfurl'] = $sysset['wxkfurl'];
				}
			}
			if(!$sysset['kfurl']) $sysset['kfurl'] = '';
			$logintype = $sysset['logintype_'.$platform];
			if(!$logintype){
				$logintype = [];
			}else{
				$logintype = explode(',',$logintype);
			}

			$data['_initdata'] = [
				'name'=>$sysset['name'],
				'logo'=>$sysset['logo'],
				'desc'=>$sysset['desc'],
				'wxkf'=>$sysset['wxkf'],
				'corpid'=>$sysset['corpid'],
				'kfurl'=>$sysset['kfurl'],
				'mid'=>$this->mid,
				'pre_url'=>PRE_URL,
				'session_id'=>$this->sessionid,
				'platform'=>platform,
				'indexurl'=>$indexurl,
				'menudata'=>$menudata,
				'menu2data'=>$menu2datalist,
				'sharelist'=>$sharelist,
				'textset'=>$textset,
				'color1'=>$sysset['color1'],
				'color2'=>$sysset['color2'],
				'color1rgb'=>hex2rgb($sysset['color1']),
				'color2rgb'=>hex2rgb($sysset['color2']),
				'logintype'=>$logintype,
				'isdouyin'=>isdouyin
			];
			if(platform == 'mp'){
				$share_package = \app\common\Wechat::share_package(aid);
				$data['share_package'] = $share_package;
			}
		}
		if((input('param.needinit') == 1 && $this->mid) || $data['mid']){
			if($data['mid']) $this->mid = $data['mid'];
			$config = include(ROOT_PATH.'config.php');
			$authtoken = $config['authtoken'];
			$token = md5(md5($authtoken.$this->mid));
			$data['socket_token'] = Db::name('member')->where('id',$this->mid)->value('random_str');
		}
		if($isexit == 1){
			echojson($data);
		}
		return json($data);
	}

	//收藏
	public function addfavorite(){
		$this->checklogin();
		$post = input('post.');
		$rs = Db::name('member_favorite')->where('aid',aid)->where('mid',mid)->where('proid',$post['proid'])->where('type',$post['type'])->find();
		if($rs){
			Db::name('member_favorite')->where('aid',aid)->where('mid',mid)->where('proid',$post['proid'])->where('type',$post['type'])->delete();
			return json(['status'=>1,'msg'=>'已取消','url'=>true]);
		}else{
			Db::name('member_favorite')->insert(['aid'=>aid,'mid'=>mid,'proid'=>$post['proid'],'type'=>$post['type'],'createtime'=>time()]);
			return json(['status'=>1,'msg'=>'已收藏','url'=>true]);
		}
	}

	//生成海报
	function _getposter($aid,$platform,$posterdata,$page,$scene,$textReplaceArr){
		set_time_limit(0);
		$posterdata = json_decode($posterdata,true);
		$poster_bg = $posterdata['poster_bg'];
		$poster_data = $posterdata['poster_data'];
		@ini_set('memory_limit', -1);

		if(strpos($poster_bg,'http') ===false){
			$poster_bg = PRE_URL.$poster_bg;
		}
		$bg = imagecreatefromstring(request_get($poster_bg));
		if($bg){
			$bgwidth = imagesx($bg);
			$bgheight = imagesy($bg);
			if($bgheight/$bgwidth > 1.92) $bgheight = floor($bgwidth * 1.92);
			$target = imagecreatetruecolor($bgwidth, $bgheight);
			imagecopy($target, $bg, 0, 0, 0, 0,$bgwidth,$bgheight);
			imagedestroy($bg);
		}else{
			$bgwidth = 680;
			$bgheight = 1080;
			$target = imagecreatetruecolor(680, 1080);
			imagefill($target,0,0,imagecolorallocate($target, 255, 255, 255));
		}
		$huansuan = $bgwidth/340;
		//$bgwidth = imagesx($bg);
		//$bgheight = imagesy($bg);
		$font = ROOT_PATH."static/fonts/msyh.ttf";
		foreach ($poster_data as $d){
			$d['left'] = intval(str_replace('px', '', $d['left'])) * $huansuan;
			$d['top'] = intval(str_replace('px', '', $d['top'])) * $huansuan;
			$d['width'] = intval(str_replace('px', '', $d['width'])) * $huansuan;
			$d['height'] = intval(str_replace('px', '', $d['height'])) * $huansuan;
			$d['size'] = intval(str_replace('px', '', $d['size'])) * $huansuan/2*1.5;

			if ($d['type'] == 'qrwx'){
				$access_token = \app\common\Wechat::access_token($aid,'wx');
				if(!$access_token){
					\think\facade\Log::write('获取微信小程序access_token失败，aid:'.$aid);
					echojson(array('status'=>0,'msg'=>'微信小程序access_token获取失败'));
				}
				
				$url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token='.$access_token;
				$data = array();
				$data['scene'] = $scene;
				$data['page'] = ltrim($page,'/');
				
				// 记录详细的API调用参数日志
				\think\facade\Log::write('小程序码生成API调用参数：'.jsonEncode([
					'url' => $url,
					'scene' => $scene,
					'page' => $data['page'],
					'aid' => $aid
				]));
				
				$res = request_post($url,jsonEncode($data));
				$errmsg =json_decode($res,true);
				
				// 记录API返回结果
				if($errmsg){
					\think\facade\Log::write('微信小程序码生成API返回错误：'.jsonEncode($errmsg));
				}
				
				if($errmsg){
					if($errmsg['errcode'] == 41030){
						echojson(array('status'=>0,'msg'=>'小程序页面路径不存在或未发布，请检查页面：'.$data['page']));
					}elseif($errmsg['errcode'] == 45009){
						echojson(array('status'=>0,'msg'=>'接口调用超过限制，请稍后重试'));
					}elseif($errmsg['errcode'] == 40001){
						echojson(array('status'=>0,'msg'=>'access_token无效或过期'));
					}else{
						echojson(array('status'=>0,'msg'=>'生成小程序码失败：'.$errmsg['errmsg'].'(错误码：'.$errmsg['errcode'].')'));
					}
				}
				//$filepath = '/upload/poster/'.$aid.'/'.date('Ym/d').'/'.date('His').rand(1000,9999).'.jpg';
				//mk_dir(dirname(ROOT_PATH.ltrim($filepath,'/')));
				//file_put_contents(ROOT_PATH.ltrim($filepath,'/'),$res);
				//$qrcode = PRE_URL.$filepath;
				//$img = imagecreatefromstring(request_get($qrcode));
				if(!$errmsg){
					$img = imagecreatefromstring($res);
					imagecopyresampled($target, $img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($img), imagesy($img));
				}else{
					//echojson(array('status'=>0,'msg'=>$errmsg['errmsg']));
				}
			} else if ($d['type'] == 'qrmp') {
				$qrcode = createqrcode(PRE_URL .'/h5/'.$aid.'.html#'.$page.'?scene='.$scene.'&t='.time());
				$img = imagecreatefromstring(request_get($qrcode));
				imagecopyresampled($target, $img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($img), imagesy($img));
			}else if($d['type'] == 'qrgz'){
				$url = 'https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token='.\app\common\Wechat::access_token($aid,'mp');
				$data = array();
				$data['action_name'] = 'QR_LIMIT_STR_SCENE';
				$data['action_info'] = ['scene'=>['scene_str'=>$scene]];
				$rs = request_post($url,jsonEncode($data));
				$rs = json_decode($rs,true);
				if($rs['url']){
					$qrcode = createqrcode($rs['url']);
					$img = imagecreatefromstring(request_get($qrcode));
					imagecopyresampled($target, $img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($img), imagesy($img));
				}else{
					//echojson(array('status'=>0,'msg'=>\app\common\Wechat::geterror($rs)));
				}
			}else if($d['type'] == 'qrpay_web'){
				// 网页付款二维码 - 修复链接格式以匹配正确的H5页面路径
				// 正确解析scene，支持bid_xxx和pid_xxx两种格式
				if(strpos($scene, 'pid_') === 0){
					// 如果是pid场景，从数据库获取对应的商家ID
					$mid = str_replace('pid_', '', $scene);
					$business = Db::name('business')->where('aid', $aid)->where('mid', $mid)->find();
					$business_id = $business ? $business['id'] : 0;
				} else {
					// 如果是bid场景，直接提取商家ID
					$business_id = str_replace('bid_', '', $scene);
				}
				// 构建URL时，bid使用商家ID，scene保持原值
				$payUrl = PRE_URL . '/h5/' . $aid . '.html#/pagesExt/maidan/pay?bid=' . $business_id . '&scene=' . $scene . '&t=' . time();
				$qrcode = createqrcode($payUrl);
				$img = imagecreatefromstring(request_get($qrcode));
				imagecopyresampled($target, $img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($img), imagesy($img));
			}else if($d['type'] == 'qrpay_mini'){
				// 小程序付款二维码
				// 正确解析scene，支持bid_xxx和pid_xxx两种格式
				if(strpos($scene, 'pid_') === 0){
					// 如果是pid场景，从数据库获取对应的商家ID
					$mid = str_replace('pid_', '', $scene);
					$business = Db::name('business')->where('aid', $aid)->where('mid', $mid)->find();
					$business_id = $business ? $business['id'] : 0;
				} else {
					// 如果是bid场景，直接提取商家ID
					$business_id = str_replace('bid_', '', $scene);
				}
				$url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token='.\app\common\Wechat::access_token($aid,'wx');
				$data = array();
				$data['scene'] = $scene; // 保持原始scene值
				$data['page'] = 'pagesExt/maidan/pay';
				$res = request_post($url,jsonEncode($data));
				$errmsg =json_decode($res,true);
				if($errmsg){
					if($errmsg['errcode'] == 41030){
						echojson(array('status'=>0,'msg'=>'小程序发布后才能生成分享海报'));
					}else{
						echojson(array('status'=>0,'msg'=>$errmsg['errmsg']));
					}
				}
				if(!$errmsg){
					$img = imagecreatefromstring($res);
					imagecopyresampled($target, $img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($img), imagesy($img));
				}
			}else if ($d['type'] == 'img') {
				if($d['src'][0] == '/') $d['src'] = PRE_URL.$d['src'];
				$img = imagecreatefromstring(request_get($d['src']));
				if($img)
				imagecopyresampled($target, $img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($img), imagesy($img));
			} else if ($d['type'] == 'text') {
				$d['content'] = str_replace(array_keys($textReplaceArr),array_values($textReplaceArr),$d['content']);
				$colors = hex2rgb($d['color']);
				$color = imagecolorallocate($target, $colors['red'], $colors['green'], $colors['blue']);
				
				// 检查是否需要居中
				$x_position = $d['left'];
				if(isset($d['center']) && $d['center'] == '1'){
					// 计算文本宽度并居中
					$text_box = imagettfbbox($d['size'], 0, $font, $d['content']);
					$text_width = $text_box[4] - $text_box[0];
					$x_position = $d['left'] + ($d['width'] - $text_width) / 2;
				}
				
				imagettftext($target, $d['size'], 0, $x_position, $d['top'] + $d['size'], $color, $font,  $d['content']);
			} else if ($d['type'] == 'textarea') {
				$d['content'] = str_replace(array_keys($textReplaceArr),array_values($textReplaceArr),$d['content']);
				$colors = hex2rgb($d['color']);
				$color = imagecolorallocate($target, $colors['red'], $colors['green'], $colors['blue']);
				$string = $d['content'];
				$_string='';
				$__string='';
				$_height = 0;
				mb_internal_encoding("UTF-8"); // 设置编码
				for($i=0;$i<mb_strlen($string);$i++){
					$box = imagettfbbox($d['size'],0,$font,$_string);
					$_string_length = $box[2]-$box[0];
					$box = imagettfbbox($d['size'],0,$font,mb_substr($string,$i,1));
					if($_string_length+$box[2]-$box[0]<$d['width']*1){
						$_string.=mb_substr($string,$i,1);
					}else{
						$_height += $box[1]-$box[7]+4;
						//var_dump($_height.'--'.$d['height']);
						if($_height >= $d['height']*1){
							break;
						}
						$__string.=$_string."\n";
						$_string=mb_substr($string,$i,1);
					}
				}
				$__string.=$_string; 
				$box=imagettfbbox($d['size'],0,$font,mb_substr($__string,0,1));
				
				// 检查是否需要居中处理多行文本
				if(isset($d['center']) && $d['center'] == '1'){
					// 将文本按换行符分割成多行
					$lines = explode("\n", $__string);
					$line_height = $box[3] - $box[7] + 4;
					$y_position = $d['top'] + ($box[3] - $box[7]);
					
					foreach($lines as $line_index => $line){
						if(trim($line) !== ''){
							// 计算每行文本的居中位置
							$line_box = imagettfbbox($d['size'], 0, $font, $line);
							$line_width = $line_box[4] - $line_box[0];
							$x_position = $d['left'] + ($d['width'] - $line_width) / 2;
							
							imagettftext($target, $d['size'], 0, $x_position, $y_position + ($line_index * $line_height), $color, $font, $line);
						}
					}
				} else {
					// 原来的左对齐逻辑
					imagettftext($target,$d['size'],0,$d['left'],$d['top']+($box[3]-$box[7]),$color,$font,$__string);
				}

			} else if ($d['type'] == 'pro_img') {
				$img = imagecreatefromstring(request_get($textReplaceArr['[商品图片]']));
				if($img)
				imagecopyresampled($target, $img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($img), imagesy($img));
			} else if ($d['type'] == 'shadow') {
				$rgba = explode(',',str_replace(array(' ','(',')','rgba'),'',$d['shadow']));
				//dump($rgba);
				$black = imagecreatetruecolor($d['width'], $d['height']);
				imagealphablending($black, false);
				imagesavealpha($black, true);
				$blackcolor = imagecolorallocatealpha($black,$rgba[0],$rgba[1],$rgba[2],(1-$rgba[3])*127);
				imagefill($black, 0, 0, $blackcolor);
				imagecopy($target, $black, $d['left'], $d['top'], 0, 0, $d['width'], $d['height']);
				imagedestroy($black);
			} else if($d['type'] == 'head') {
				$src_img = imagecreatefromstring(request_get($textReplaceArr['[头像]']));
				if($src_img){
					$w = imagesx($src_img);
					$h = imagesy($src_img);
					$radius = $d['radius']*0.01*$w/2;
					if($radius > 0){
						$img = imagecreatetruecolor($w, $h);
						//这一句一定要有
						imagesavealpha($img, true);
						//拾取一个完全透明的颜色,最后一个参数127为全透明
						$bg = imagecolorallocatealpha($img, 255, 255, 255, 127);
						imagefill($img, 0, 0, $bg);
						$r = $radius; //圆 角半径
						for ($x = 0; $x < $w; $x++) {
							for ($y = 0; $y < $h; $y++) {
								$rgbColor = imagecolorat($src_img, $x, $y);
								if (($x >= $radius && $x <= ($w - $radius)) || ($y >= $radius && $y <= ($h - $radius))) {
									//不在四角的范围内,直接画
									imagesetpixel($img, $x, $y, $rgbColor);
								} else {
									//在四角的范围内选择画
									//上左
									$y_x = $r; //圆心X坐标
									$y_y = $r; //圆心Y坐标
									if (((($x - $y_x) * ($x - $y_x) + ($y - $y_y) * ($y - $y_y)) <= ($r * $r))) {
										imagesetpixel($img, $x, $y, $rgbColor);
									}
									//上右
									$y_x = $w - $r; //圆心X坐标
									$y_y = $r; //圆心Y坐标
									if (((($x - $y_x) * ($x - $y_x) + ($y - $y_y) * ($y - $y_y)) <= ($r * $r))) {
										imagesetpixel($img, $x, $y, $rgbColor);
									}
									//下左
									$y_x = $r; //圆心X坐标
									$y_y = $h - $r; //圆心Y坐标
									if (((($x - $y_x) * ($x - $y_x) + ($y - $y_y) * ($y - $y_y)) <= ($r * $r))) {
										imagesetpixel($img, $x, $y, $rgbColor);
									}
									//下右
									$y_x = $w - $r; //圆心X坐标
									$y_y = $h - $r; //圆心Y坐标
									if (((($x - $y_x) * ($x - $y_x) + ($y - $y_y) * ($y - $y_y)) <= ($r * $r))) {
										imagesetpixel($img, $x, $y, $rgbColor);
									}
								}
							}
						}
						imagecopyresampled($target, $img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($img), imagesy($img));
					}else{
						imagecopyresampled($target, $src_img, $d['left'], $d['top'], 0, 0, $d['width'], $d['height'],imagesx($src_img), imagesy($src_img));
					}
				}
			}
		}
		$url = '/upload/'.date('Ym/d_His').rand(1000,9999).'.jpg';
		$filepath = ROOT_PATH.ltrim($url,'/');
		mk_dir(dirname($filepath));
		imagejpeg($target,$filepath,100);
		return PRE_URL.$url;
	}
	
	//商品列表数据会员价处理
	public function formatprolist($datalist){
		if($this->member){
			foreach($datalist as $k=>$v){
				if($v['lvprice']==1){
					$lvprice_data = json_decode($v['lvprice_data'],true);
					if($lvprice_data && isset($lvprice_data[$this->member['levelid']])){
						$datalist[$k]['sell_price'] = $lvprice_data[$this->member['levelid']];
					}
				}
			}
		}
		$shopset = Db::name('shop_sysset')->where('aid',aid)->find();
		if($shopset){
			foreach($datalist as $k=>$v){
				if($shopset['hide_sales']==1){
					$datalist[$k]['sales'] = 0;
				}
			}
		}

		// 2024-07-09 - 隐藏价格
		foreach ($datalist as $key => $value) {			
			if($value['show_price'] > 0) {
			    //限制等级
			    $levelids = explode(',', $value['show_price']);
			    if(!in_array($this->member['levelid'], $levelids)) {
			    	$datalist[$key]['sell_price'] = $value['hide_price_text'];
			    	unset($datalist[$key]['market_price']);
			    }
			}

		}
		return $datalist;
	}
	//商品数据会员价处理
	public function formatproduct($product){
        $product['sell_price_origin'] = $product['sell_price'];
		if(!$this->member) return $product;
		if($product['lvprice']==1){
			$lvprice_data = json_decode($product['lvprice_data'],true);
			if($lvprice_data && isset($lvprice_data[$this->member['levelid']])){
				$product['sell_price'] = $lvprice_data[$this->member['levelid']];
			}
		}
		return $product;
	}
    //商品数据会员价处理
    public function formatScoreProduct($product){
        if(!$this->member) return $product;
        if($product['lvprice']==1){
            $lvprice_data = json_decode($product['lvprice_data'],true);
            if($lvprice_data && isset($lvprice_data[$this->member['levelid']])){
                if(isset($lvprice_data[$this->member['levelid']]['money_price']))
                $product['money_price'] = $lvprice_data[$this->member['levelid']]['money_price'];
                if(isset($lvprice_data[$this->member['levelid']]['score_price']))
                $product['score_price'] = $lvprice_data[$this->member['levelid']]['score_price'];
            }
        }
        return $product;
    }
	//商品规格列表数据会员价处理
	public function formatgglist($gglist, $bid = 0){
		if(!$this->member) return $gglist;
		foreach($gglist as $k=>$v){
			$lvprice_data = json_decode($v['lvprice_data'],true);
			if($lvprice_data && isset($lvprice_data[$this->member['levelid']])){
			    if($bid && getcustom('plug_businessqr') && $lvprice_data[$this->member['levelid']] == 0) {
			        $show_business = Db::name('member_level')->where('id', $this->member['levelid'])->value('show_business');
			        if($show_business) {
                        $gglist[$k]['sell_price'] = $lvprice_data[$this->member['levelid']];
                    }
                } else {
                    $gglist[$k]['sell_price'] = $lvprice_data[$this->member['levelid']];
                }
			}
		}
		return $gglist;
	}
	public function formatguige($guige, $bid = 0){
		if(!$this->member) return $guige;
		$lvprice_data = json_decode($guige['lvprice_data'],true);
		if($lvprice_data && isset($lvprice_data[$this->member['levelid']])){
            if($bid && getcustom('plug_businessqr') && $lvprice_data[$this->member['levelid']] == 0) {
                $show_business = Db::name('member_level')->where('id', $this->member['levelid'])->value('show_business');
                if($show_business) {
                    $guige['sell_price'] = $lvprice_data[$this->member['levelid']];
                }
            } else {
                $guige['sell_price'] = $lvprice_data[$this->member['levelid']];
            }
		}
		return $guige;
	}
}