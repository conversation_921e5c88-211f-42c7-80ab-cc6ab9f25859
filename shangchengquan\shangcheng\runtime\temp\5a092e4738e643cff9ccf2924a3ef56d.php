<?php /*a:4:{s:82:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\index.html";i:1751943123;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;s:79:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\copyright.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>页面列表</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-list"></i> 页面列表</div>
          <div class="layui-card-body" pad15>

						<div class="layui-form layui-col-md12" style="padding-bottom:10px;padding-left:10px">
							<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openmax('<?php echo url('edit'); ?>')">添加</a>
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">标题</label>
								<div class="layui-input-block" style="width:200px;margin-left:90px">
									<input type="text" name="name" autocomplete="off" class="layui-input" value="">
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layui-btn-primary layuiadmin-btn-replys" lay-submit="" lay-filter="LAY-app-forumreply-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<div class="layui-col-md12">
							
								<div id="datalist">
									<?php foreach($datalist as $v): ?>
									<div style="float: left; margin:9px 10px; padding:12px; width: 250px; background-color: #fff; box-shadow: 0 0 2px #919dab;">
										<h1 style="width: 250px; height: 20px; line-height: 20px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: normal; font-size:14px; color: #282828;" class="flex-y-center">
											<?php if($v['ishome']=='2'): ?><span style="color:#fb6b5b">[会员中心]</span><?php endif; if($v['ishome']=='1'): ?><span style="color:#fb6b5b">[首页]</span><?php endif; if($v['ishome']=='9'): ?><span style="color:#fb6b5b">[建设城市]</span><?php endif; ?>
											&nbsp;<?php echo $v['name']; ?>
										</h1>
										<div style="height: 20px; line-height: 20px; margin-bottom: 6px; color: #282828; font-size: 12px;"><?php echo date('Y-m-d H:i',$v['createtime']); ?></div>
										<div style="width: 250px; height:400px;overflow: hidden;">
											<iframe style="height:400px; width: 248px; overflow-y: hidden;border:1px solid #eee" frameborder="0" src="<?php echo url('preview'); ?>/id/<?php echo $v['id']; ?>" width="100%"></iframe>
										</div>
										<div class="clearfix" style="border-top:1px solid #e6ebf1; padding:13px 0 5px;">
											<?php if($v['ishome']=='0'): if($auth['del']): ?><a href="javascript:void(0)" onclick="datadel(<?php echo $v['id']; ?>)" class="fa fa-trash-o" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="删除"></a><?php endif; ?>
											<?php endif; ?>
											<a href="javascript:void(0)" onclick="pagecopy(<?php echo $v['id']; ?>)" class="fa fa-copy" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="复制"></a>
											<a class="fa fa-edit" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="编辑" href="javascript:void(0)" onclick="openmax('<?php echo url('edit'); ?>/id/<?php echo $v['id']; ?>')"></a>
											<?php if($v['ishome']=='0'): if($auth['setHome']): ?><a href="javascript:void(0)" onclick="sethome(<?php echo $v['id']; ?>)" class="fa fa-home" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="设为首页"></a><?php endif; elseif($bid>0): if($auth['reHome']): ?><a href="javascript:void(0)" onclick="rehome(<?php echo $v['id']; ?>)" class="fa fa-home" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="取消首页"></a><?php endif; ?>
											<?php endif; ?>
											<a href="javascript:void(0)" onclick="showqr(<?php echo $v['id']; ?>,<?php echo $v['ishome']; ?>)" class="fa fa-qrcode" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="查看"></a>
										</div>
									</div>
									<?php endforeach; ?>
								</div>
								<div class="layui-col-md12 layui-col-sm12">
										<div id="demo0"></div>
								</div>
							</div>
							<!-- <table id="tabledata" lay-filter="tabledata"></table> -->
						</div>
          </div>
        </div>
    </div>
  </div>
	<script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
	<script>
	var wheredata = {op:'getdata',limit:10,page:1};
	layui.laypage.render({
		elem: 'demo0',
		limit:wheredata.limit,
		layout:['prev', 'page', 'next'],
		count: <?php echo $count; ?>, //数据总数
		jump:function(obj,first){
			if(!first){
				wheredata.page = obj.curr
				getdata()
			}
		}
	});
	function getdata(){
		var index =layer.load();
		$.get('',wheredata,function(res){
			layer.close(index);
			var html = '';
			for(var i in res.data){
				var item = res.data[i]
				html+='<div style="float: left; margin:9px 10px; padding:12px; width: 250px; background-color: #fff; box-shadow: 0 0 2px #919dab;">';
				html+='	<h1 style="width: 250px; height: 20px; line-height: 20px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: normal; font-size:14px; color: #282828;" class="flex-y-center">'
				if(item.ishome=='2'){
				html+= '<span style="color:#fb6b5b">[会员中心]</span>'
				}
				if(item.ishome=='1'){
				html+= '<span style="color:#fb6b5b">[首页]</span>'
				}
				html+= item.name+'</h1>';
				html+='	<div style="height: 20px; line-height: 20px; margin-bottom: 6px; color: #282828; font-size: 12px;">'+date("Y-m-d H:i",item.createtime)+'</div>';
				html+='	<div class="" style="width: 250px; height:400px;overflow: hidden;">';
				html+='		<iframe style="height:400px; width: 248px; overflow-y: hidden;border:1px solid #eee" frameborder="0" src="<?php echo url('preview'); ?>/id/'+item.id+'" class="iframe_page" width="100%"></iframe>';
				html+='	</div>';
				html+='	<div class="clearfix" style="border-top:1px solid #e6ebf1; padding:13px 0 5px;">';
				if(item.ishome=='0'){
					<?php if($auth['del']): ?> html+='		<a href="javascript:void(0)" onclick="datadel('+item.id+')" class="fa fa-remove" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="删除" rel="nofollow"></a>';<?php endif; ?>
				}
				html+='		<a href="javascript:void(0)" onclick="pagecopy('+item.id+')" class="fa fa-copy" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="复制"></a>';
				html+='		<a href="javascript:void(0)" onclick="openmax(\'<?php echo url('edit'); ?>/id/'+item.id+'\')" class="fa fa-edit" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="编辑"></a>';
				if(item.ishome=='0'){
					<?php if($auth['setHome']): ?>html+='		<a href="javascript:void(0)" onclick="sethome('+item.id+')" class="fa fa-home" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="设为首页"></a>';<?php endif; ?>
				}
				html+='		<a href="javascript:void(0)" onclick="showqr('+item.id+','+item.ishome+')" class="fa fa-qrcode" style="text-decoration: none; float: right; font-size: 18px; padding:0 8px; color: #6c707f;" title="查看"></a>';
				html+='	</div>';
				html+='</div>';
			}
			$('#datalist').html(html);
			layui.laypage.render({
				elem: 'demo1',
				curr:wheredata.page,
				limit:wheredata.limit,
				layout:['prev', 'page', 'next'],
				//count: res.count, //数据总数
				jump:function(obj,first){
					if(!first){
						wheredata.page = obj.curr
						getdata()
					}
				}
			});
		});
	}
	//检索
	layui.form.on('submit(LAY-app-forumreply-search)', function(obj){
		var field = obj.field
		wheredata.page = 1;
		wheredata.name = field.name
		getdata()
	})
	function openedit(id){
		layer.open({type:2,content:"<?php echo url('edit'); ?>/id/"+id,title:false,area:['100%','100%'],closeBtn:0,scrollbar:false})
	}
	function pagecopy(id){
		layer.confirm('确定要复制吗?',{icon: 7, title:'操作确认'}, function(index){
			layer.close(index);
			var index = layer.load();
			$.post("<?php echo url('copy'); ?>",{id:id},function(data){
				layer.close(index);
				dialog(data.msg,data.status,location.href);
			})
		})
	}
	function rehome(id){
		layer.confirm('确定要取消首页设置吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("<?php echo url('rehome'); ?>",{id:id,ishome:1},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				reload()
			})
		});
	}
	//删除
	function datadel(id){
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			var id = [];
			for(var i=0;i<checkData.length;i++){
				id.push(checkData[i]['id']);
			}
		}
		layer.confirm('确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("<?php echo url('del'); ?>",{id:id},function(data){
				layer.close(index);
				dialog(data.msg,data.status,location.href);
			})
		});
	}
	//设为首页
	function sethome(id){
		layer.open({type:0,content:'是否要将本页面设计为首页?',title:false,btn:['设为首页',<?php if($bid==0): ?>'设为会员中心',<?php endif; ?>'取消'],
			yes:function(){
				var index = layer.load();
				$.post("<?php echo url('sethome'); ?>",{id:id,ishome:1},function(data){
					layer.close(index);
					dialog(data.msg,data.status);
					reload()
				})
			},
			btn2:function(){
				var index = layer.load();
				$.post("<?php echo url('sethome'); ?>",{id:id,ishome:2},function(data){
					layer.close(index);
					dialog(data.msg,data.status);
					reload()
				})
			}
		});
	}
	function openmax2(url){
		var index = layer.open({type:2,content:url,title:'111',closeBtn:0,scrollbar:false,moveOut:true,maxmin:true})
		layer.full(index);
	}
	function showqr(id,ishome){
		if(ishome==1){
			if('<?php echo $bid; ?>'==0){
				var url = '<?php echo m_url('/pages/index/index'); ?>';
			}else{
				var url = '<?php echo m_url('pagesExt/business/index?id='.$bid); ?>';
			}
		}else if(ishome==2){
			var url = '<?php echo m_url('/pages/my/usercenter'); ?>';
		}else{
			var url = '<?php echo m_url('/pages/index/main'); ?>?id='+id
		}
		layer.open({
			type:1,
			area:['350px','420px'],
			content:'<div style="margin:auto auto;text-align:center"><div style="margin:10px 30px" id="returl"></div><div style="font-size:12px;color:#666;word-break:break-all;padding:0 10px">'+url+'</div></div>',
			title:'预览',
			shadeClose:true
		});
		var qrcode = new QRCode('returl', {
			text: 'your content',
			width: 280,
			height: 280,
			colorDark : '#000000',
			colorLight : '#ffffff',
			correctLevel : QRCode.CorrectLevel.L
		});
		qrcode.clear();
		qrcode.makeCode(url);
	}
	</script>
	
</body>
</html>