<?php /*a:52:{s:84:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\preview.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-topbar.html";i:1747488569;s:91:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-shop.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-notice.html";i:1745486434;s:91:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-menu.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-banner.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-picture.html";i:1745486434;s:95:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-pictures.html";i:1745486434;s:92:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-title.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-dhlist.html";i:1745486434;s:91:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-text.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-search.html";i:1752826962;s:96:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-lbssearch.html";i:1745486434;s:91:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-line.html";i:1745486434;s:92:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-blank.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-product.html";i:1745486434;s:92:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-cycle.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-collage.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-kanjia.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-seckill.html";i:1745486434;s:96:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-scoreshop.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-coupon.html";i:1745486434;s:95:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-richtext.html";i:1745486434;s:91:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-cube.html";i:1745486434;s:92:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-video.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-article.html";i:1745486434;s:91:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-form.html";i:1745486434;s:90:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-map.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-button.html";i:1745486434;s:92:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-cover.html";i:1745486434;s:95:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-business.html";i:1745486434;s:97:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-shortvideo.html";i:1745486434;s:95:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-liveroom.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-hotspot.html";i:1745486434;s:95:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-userinfo.html";i:1748664610;s:99:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-supervipcard.html";i:1745486434;s:92:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-yuyue.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-tuangou.html";i:1745486434;s:99:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-luckycollage.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-kecheng.html";i:1747236764;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-zhaopin.html";i:1745486434;s:94:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\edit-zhaopin.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-qiuzhi.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\edit-qiuzhi.html";i:1745486434;s:91:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-wxad.html";i:1745486434;s:105:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-restaurant_product.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-jidian.html";i:1745486434;s:90:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-tab.html";i:1745486434;s:95:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-form-log.html";i:1745486434;s:93:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-venues.html";i:1745486434;s:103:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-electricity_form.html";i:1745486434;s:98:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\designer_page\temp\show-daihuoyiuan.html";i:1745486434;}*/ ?>
<html ng-app="myApp">
<head>
<meta charset="utf-8">
<title><?php echo $info['name']; ?></title>
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" id="viewport" name="viewport">
<meta name="format-detection" content="telephone=no" />
<link rel="stylesheet" href="/static/admin/layui/css/layui.css" media="all">
<link rel="stylesheet" href="/static/imgsrc/designer.css?v=20220803">
<link rel="stylesheet" href="/static/admin/css/font-awesome.min.css" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
<style>
*{box-sizing:border-box}
body{zoom:72.8%;margin:0px; background:#f7f7f8;box-sizing:border-box;}
body::-webkit-scrollbar {width: 0px;height: 0px;}
body::-webkit-scrollbar-thumb {border-radius: 0px;-webkit-box-shadow: inset 0 0 0px rgba(0,0,0,0.1);background: rgba(0,0,0,0.2);}
body::-webkit-scrollbar-track {-webkit-box-shadow: inset 0 0 0px rgba(0,0,0,0.5);border-radius: 0;background: rgba(0,0,0,0);}
.dsn-mod,.dsn-mod:hover{border:0px;cursor:default;}
.dsn-cube td {height:auto;}

.flex {display: -webkit-box;display: -webkit-flex;display: flex;}
.flex1 {flex-grow: 1;flex-shrink: 1;}
.flex0 {flex-grow: 0;flex-shrink: 0;}
.flex-row {display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-orient: horizontal;-webkit-flex-direction: row;flex-direction: row;}
.flex-col{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-orient: vertical;-webkit-flex-direction: column;flex-direction: column;}
.flex-x-center{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-pack: center;-webkit-justify-content: center;-ms-flex-pack: center;justify-content: center;}
.flex-y-center{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-align: center;-webkit-align-items: center;-ms-flex-align: center;-ms-grid-row-align: center;align-items: center;}
.flex-y-bottom{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-align: end;-webkit-align-items: flex-end;-ms-flex-align: end;-ms-grid-row-align: flex-end;align-items: flex-end;}
</style>
</head>
<body>
<div ng-controller="MainCtrl">
	<div ng-repeat="Item in Items" class="dsn-repeat">
		<div ng-include="'show-'+Item.temp+'.html'" class="dsn-parent" id="{{Item.id}}" mid="{{Item.id}}" on-finish-render-filters></div>
	</div>
	<div style="height: 50px;" ng-show="pages[0].params.footer==2"></div>

	<script type="text/ng-template" id="show-topbar.html"><div class="dsn-mod dsn-topbar dsn-mod-nohover" style="color:#fff;background:#333;cursor:pointer">
	<div style="float:left;width:100%;font-size:12px">
		<div style="float:left;width:30%">&nbsp;<i class="fa fa-signal"></i> wechat <i class="fa fa-wifi"></i></div>
		<div style="float:left;text-align:center;width:40%">12:00</div>
		<div style="float:left;text-align:right;width:30%">100% <i class="fa fa-battery-full"></i>&nbsp;</div>
	</div>
	<div style="float:left;width:98%;margin:2px 1% 0 1%;">
		<div style="float:left;width:30%">&nbsp;</div>
		<div style="float:left;text-align:center;width:40%;font-size:16px;height:27px;line-height:27px">{{page.params.title || '默认标题'}}</div>
		<div style="float:right;width:30%;border-radius:20px;width:70px;height: 25px;border:1px solid rgba(255,255,255,0.2);text;text-align:center;overflow:hidden;display:none">
			<div style="float:left;width:49%;font-size:17px;height:27px;line-height:27px">
				<div style="float:left;margin-left:2px">&nbsp;&bull;</div>
				<div style="float:left;font-size:27px">&bull;</div>
				<div style="float:left;">&bull;</div>
			</div>
			<div style="float:left;width:2%;"><span style="border-right:1px solid;opacity:0.2;"></span></div>
			<div style="float:right;width:49%;"><i class="fa fa-dot-circle-o" style="font-size:19px;height:25px;line-height:25px"></i></div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-shop.html"><!-- 样式1 -->
<div class="dsn-mod dsn-shop1" ng-class="{'dsn-mod-select':Item.id == focus}" ng-show="Item.params.style == 1">
	<img class="img" ng-src="{{Item.params.bgimg}}"/>
	<div class="dsn-shop1-menu" ng-show="Item.params.menu == 1"  ng-style="{'color':Item.params.navcolor}">
		<a ng-repeat="menu in Item.data" href="javascript:void(0);"><div class="dsn-shop1-nav"><img src="{{menu.imgurl}}" style="width:20px;height:20px"><br>{{menu.text}}</div></a>
	</div>
	<div class="dsn-shop1-shopname" ng-show="Item.params.name == 1" style="font-size: 18px;">
		<div class="dsn-shop1-name">{{system.name}}</div>
	</div>
	<div class="dsn-shop1-shoplogo" ng-show="Item.params.logo == 1"> 
		<div class="dsn-shop1-shoplogo-img">
			<img class="img" ng-src="{{system.logo || '<?php echo PRE_URL; ?>/imgsrc/picture-1.jpg'}}"/>
		</div>
	</div>
</div>
<!-- 样式2 -->
<div class="dsn-mod dsn-shop2" ng-class="{'dsn-mod-select':Item.id == focus}" ng-show="Item.params.style == 2">
	<img class="shop2-img" ng-src="{{Item.params.bgimg}}"/>
	<div class="shop2-shoplogo" ng-show="Item.params.logo == 1">
		<img class="shop2-shoplogo-img" ng-src="{{system.logo}}"/>
	</div>
	<div class="shop2-shopname" ng-show="Item.params.name == 1">{{system.name}}</div>
	<div class="shop2-menu" ng-show="Item.params.menu == 1" ng-style="{'color':Item.params.navcolor}">
		<div class="shop2-nav" ng-repeat="menu in Item.data">
			<img src="{{menu.imgurl}}" style="width:18px;height:18px">
			<div style="font-size:12px;margin-top:3px">{{menu.text}}</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-notice.html"><div class="dsn-mod dsn-notice2" ng-class="{'dsn-mod-select':Item.id == focus}" style="color:{{Item.params.color}};background-color:{{Item.params.bgcolor}};margin:{{Item.params.margin_y}}px {{Item.params.margin_x}}px 0;padding:{{Item.params.padding_y}}px {{Item.params.padding_x}}px">
	<div class="left" ng-show="Item.params.showimg==1"><img class="image" src="{{Item.params.img}}"/></div>
	<div class="right">
		<img class="ico" src="{{Item.params.icon}}" ng-show="Item.params.showicon==1"/>
		<div class="itemlist" style="font-size:{{Item.params.fontsize}}px">
			<div class="item" ng-repeat="d in Item.data">{{d.title}}</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-menu.html"><div class="dsn-mod dsn-menu" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'font-size':Item.params.fontsize+'px','background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':(Item.params.padding_y*1+8)+'px '+Item.params.padding_x+'px '+Item.params.padding_y+'px '+Item.params.padding_x+'px','border-radius':Item.params.boxradius + 'px'}">
	<div class="dsn-menu-title" ng-if="Item.params.showtitle==1" style="color:{{Item.params.titlecolor}};font-size:{{Item.params.titlesize}}px">{{Item.params.title}}</div>
	<div class="dsn-menu-nav{{Item.params.num}} {{Item.params.showicon==0 && Item.params.showline==1?'showline':''}}" href="{{menu.hrefurl|| 'javascript:void(0);'}}" ng-repeat="menu in Item.data" ng-hide="$index >= Item.params.pernum">
		<img ng-show="Item.params.showicon==1" src="{{menu.imgurl|| '/static/imgsrc/picture-1.jpg'}}" style="border-radius:{{Item.params.radius/2}}%;width:{{Item.params.iconsize}}px;height:{{Item.params.iconsize}}px"/>
		<div class="dsn-menu-text" ng-style="{'color':menu.color,'height':Item.params.fontheight+'px','line-height':Item.params.fontheight+'px'}">{{menu.text|| '按钮文字'}}</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-banner.html"><div class="dsn-mod dsn-banner" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px','font-size':Item.params.fontsize+'px', 'position': 'relative', 'overflow': 'hidden'}">
    <!-- 背景图 -->
    <div ng-if="Item.params.bgimg" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-position: center; background-size: cover; background-repeat: no-repeat; opacity: 0.1;" ng-style="{'background-image': 'url('+Item.params.bgimg+')'}"></div>
    
    <!-- 轮播图结构 -->
    <div class="swipe" style="position: relative; z-index: 2;" ng-style="{'border-radius':Item.params.borderradius+'px', 'margin-top': (Item.params.main_title || Item.params.sub_title || Item.params.more_text) ? '60px' : '0'}">
        <div class="swipe-wrap" ng-style="{'height':Item.params.height+'px'}">
            <li ng-repeat="d in Item.data">
                <a href="javascript:void(0);">
                    <img ng-src="{{d.imgurl}}" style="width: 100%; height: 100%; object-fit: cover;"/>
                </a>
            </li>
        </div>
    </div>
    
    <!-- 标题区域 -->
    <div ng-if="Item.params.main_title || Item.params.sub_title || Item.params.more_text" style="position: absolute; top: 0; left: 0; right: 0; padding: 15px 15px 0; display: flex; justify-content: space-between; align-items: center; z-index: 3;">
        <div class="title-area" style="text-align: left; flex: 1; order: 1;">
            <div class="main-title" ng-if="Item.params.main_title" style="font-size: 16px; font-weight: bold; margin-bottom: 4px; color: {{Item.params.main_title_color || '#333'}}">{{Item.params.main_title}}</div>
            <div class="sub-title" ng-if="Item.params.sub_title" style="font-size: 12px; color: {{Item.params.sub_title_color || '#666'}}">{{Item.params.sub_title}}</div>
        </div>
        <a class="more-link" ng-if="Item.params.more_text" ng-href="{{Item.params.hrefurl}}" style="order: 2; display: inline-flex; align-items: center; font-size: 12px; text-decoration: none; padding: 4px 12px; margin-left: 15px; align-self: center; color: {{Item.params.more_text_color || '#666'}}">
            {{Item.params.more_text}}
            <i class="fa fa-angle-right" style="margin-left: 4px;"></i>
        </a>
    </div>

    <!-- 指示点 -->
    <div class='dsn-banner-dots' ng-show="Item.data.length > 1 && Item.params.indicatordots=='1'" style="position: relative; z-index: 3;" ng-style="{'left':Item.params.padding_x+'px','justify-content':Item.params.align=='center'?'center':(Item.params.align=='left'?'flex-start':'flex-end')}" indicatorcolor="{{Item.params.indicatorcolor}}" indicatoractivecolor="{{Item.params.indicatoractivecolor}}" dotactivewidth="{{Item.params.shape==''?'13px':'6px'}}" dotwidth="{{Item.params.shape==''?'3px':'6px'}}"> 
        <!-- 指示点代码保持不变 -->
        <div ng-if="Item.params.shape==''" ng-repeat="d in Item.data" style="display:inline-block; list-style: none;display:flex">
            <a ng-if="$index==0" class="shape0" ng-style="{'background-color':Item.params.indicatoractivecolor,'width':'13px'}"></a>
            <a ng-if="$index!=0" class="shape0" ng-style="{'background-color':Item.params.indicatorcolor}"></a>
        </div>
        <div ng-if="Item.params.shape=='shape1'" ng-repeat="d in Item.data" style="display:inline-block; list-style: none;display:flex">
            <a ng-if="$index==0" class="shape1" ng-style="{'background-color':Item.params.indicatoractivecolor}"></a>
            <a ng-if="$index!=0" class="shape1" ng-style="{'background-color':Item.params.indicatorcolor}"></a>
        </div>
        <div ng-if="Item.params.shape=='shape2'" ng-repeat="d in Item.data" style="display:inline-block; list-style: none;display:flex">
            <a ng-if="$index==0" class="shape2" ng-style="{'background-color':Item.params.indicatoractivecolor}"></a>
            <a ng-if="$index!=0" class="shape2" ng-style="{'background-color':Item.params.indicatorcolor}"></a>
        </div>
        <div ng-if="Item.params.shape=='shape3'" ng-repeat="d in Item.data" style="display:inline-block; list-style: none;display:flex">
            <a ng-if="$index==0" class="shape3" ng-style="{'background-color':Item.params.indicatoractivecolor}"></a>
            <a ng-if="$index!=0" class="shape3" ng-style="{'background-color':Item.params.indicatorcolor}"></a>
        </div>
    </div>
</div></script>
	<script type="text/ng-template" id="show-picture.html"><div class="dsn-mod dsn-picture" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'height':Item.params.height,'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div ng-repeat="d in Item.data">
		<a href="javascript:void(0);">
			<img ng-src="{{d.imgurl}}" ng-show="d.imgurl" ng-style="{'border-radius':Item.params.borderradius+'px'}"/>
		</a>
	</div>
</div></script>
	<script type="text/ng-template" id="show-pictures.html"><div class="dsn-mod dsn-pictures" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0'}">
	<div ng-if="Item.params.style=='2'" class="listitem2">
		<div ng-repeat="d in Item.data" class="item" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<img ng-src="{{d.imgurl}}"/>
		</div>
	</div>
	<div ng-if="Item.params.style=='3'" class="listitem3">
		<div ng-repeat="d in Item.data" class="item" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<img ng-src="{{d.imgurl}}"/>
		</div>
	</div>
	<div ng-if="Item.params.style=='4'" class="listitem4">
		<div ng-repeat="d in Item.data" class="item" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<img ng-src="{{d.imgurl}}"/>
		</div>
	</div>
	<div ng-if="Item.params.style=='5'" class="listitem5">
		<div class="left" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<img ng-src="{{Item.data[0].imgurl}}"/>
		</div>
		<div ng-if="Item.data.length > 2" class="right">
			<div class="top" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
				<img ng-src="{{Item.data[1].imgurl}}"/>
			</div>
			<div ng-if="Item.data.length > 3" class="bottom">
				<div class="left" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
					<img ng-src="{{Item.data[2].imgurl}}"/>
				</div>
				<div class="right" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
					<img ng-src="{{Item.data[3].imgurl}}"/>
				</div>
			</div>
			<div ng-if="Item.data.length == 3" class="bottom" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
				<img ng-src="{{Item.data[2].imgurl}}"/>
			</div>
		</div>
		<div ng-if="Item.data.length == 2" class="right" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<img ng-src="{{Item.data[1].imgurl}}"/>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-title.html"><div class="dsn-mod dsn-title" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'text-align':Item.params.align,'color':Item.params.color,'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div class="dsn-title-s1">
		<div class="dsn-title-s1-line" ng-style="{'border-color':Item.params.linecolor}"></div>
		<div class="dsn-title-s1-text">
			<div class="dsn-title-s1-text1" ng-style="{'font-size':Item.params.fontsize+'px','color':Item.params.color}">
				<img src="{{Item.params.imgurl}}" ng-show="Item.params.showicon==1" style="width:{{Item.params.iconsize}}px"/>
				<span class="dsn-title-s1-title1" ng-show="Item.params.title!=''">{{Item.params.title}}</span>
			</div>
		</div>
		<div class="dsn-title-s1-line" ng-style="{'border-color':Item.params.linecolor}"></div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-dhlist.html"><div class="dsn-mod dsn-dhlist" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'color':Item.params.color,'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0'}">
	<div style="line-height: 40px; text-align: center; color: #999; font-size: 16px;" ng-show="Item.data == ''">一个导航都没有...</div>
	<div class="dsn-dhlist-item" ng-repeat="d in Item.data" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
		<div class="dsn-dhlist-text1" ng-style="{'font-size':Item.params.fontsize1+'px','color':Item.params.color1}">
			<img src="{{d.imgurl}}" ng-show="d.showicon==1" style="width:{{d.iconsize}}px"/>
			<span class="dsn-dhlist-title1" ng-show="d.title1!=''" ng-style="{'margin-left':Item.params.titlemarginleft+'px'}">{{d.title1}}</span>
		</div>
		<div class="dsn-dhlist-text2" ng-style="{'font-size':Item.params.fontsize2+'px','color':Item.params.color2}">{{d.title2}}</div>
		<img src="/static/img/arrowright.png" style="width:16px;height:16px" ng-show="Item.params.arrowshow==1"/>
	</div>
</div></script>
	<script type="text/ng-template" id="show-text.html"><div class="dsn-mod dsn-text" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'text-align':Item.params.align,'color':Item.params.color,'background':Item.params.bgpic ? 'url('+Item.params.bgpic+')' : Item.params.bgcolor,'background-size':'100%','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
    <div style="white-space:pre-wrap;" ng-style="{'font-size':Item.params.fontsize+'px','line-height':Item.params.lineheight+'px','letter-spacing':Item.params.letter_spacing+'px','font-weight':(Item.params.fontbold?'bold':'normal')}">{{Item.params.content}}</div>
</div></script>
	<script type="text/ng-template" id="show-search.html"><style>
	    .dsn-search{
	        display:flex;
	    }
	    .dsn-position-left,.dsn-position-right{
	        display:inline-block;
	        padding:5px;
	        margin-right:10px;
	        width:40px !important;
	    }
	    .dsn-position-right{
	        margin-left:10px;
	    }
	    .dp-search-search{
	        flex:1;
	        display: flex;
	        align-items: center;
	    }
	    .dp-search-search-f2 {
	        flex: 1;
	    }
	    .dp-search-search-input {
	        width: 100%;
	        border: none;
	        background: transparent;
	        outline: none;
	        height: 30px;
	        line-height: 30px;
	        padding: 0 10px;
	    }
	    .dp-search-btn {
	        margin-right: 5px;
	        padding: 0 15px;
	        height: 30px;
	        line-height: 30px;
	        text-align: center;
	        cursor: pointer;
	        display: flex;
	        align-items: center;
	        justify-content: center;
	    }
	    .dp-search-btn i {
	        font-size: 16px;
	    }
	    /* 自定义图标样式 */
	    .dp-search-btn img {
	        width: 18px;
	        height: 18px;
	        display: inline-block !important;
	        vertical-align: middle;
	        object-fit: contain;
	    }
</style>

<div class="dsn-mod dsn-search" ng-class="{'dsn-mod-select':Item.id == focus}"
     ng-style="Item.params.bg_type == 'image' ?
              {'background-image':'url('+Item.params.bgimg+')', 'background-size':'cover', 'background-position':'center center', 'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'} :
              (Item.params.bg_transparent == '1' ?
               {'background-color':'transparent','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'} :
               {'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'})">
	
	
	<!--门店定位-->
	
	<div class="dsn-position-left" ng-if="Item.params.position==1">
	    <span ng-if="Item.params.areamode==0" ng-style="{color:Item.params.area_color}">{{Item.params.areamode_str}}</span>
	    <span ng-if="Item.params.areamode==1" ng-style="{color:Item.params.area_color}">{{Item.params.areamode_str}}</span>
	    <span ng-if="Item.params.areamode==2" ng-style="{color:Item.params.area_color}">{{Item.params.areamode_str}}</span>
	</div>
	

	
	
	<div ng-show="!Item.params.openmore || Item.params.openmore<=0" class="dp-search-search"
		ng-style="{'border-color':Item.params.bordercolor,'border-radius':Item.params.borderradius+'px'}" >
		<div class="dp-search-search-f1">
		   
		</div>
		<div class="dp-search-search-f2">
			<input class="dp-search-search-input" name="keyword" placeholder="{{Item.params.placeholder}}" ng-style="{color:Item.params.color}" />
		</div>
		
		<!-- 搜索按钮 -->
		<div class="dp-search-btn" ng-if="Item.params.search_btn == 1" 
		     ng-style="{
		         'background-color': Item.params.btn_color || '#ffffff', 
		         'color': Item.params.btn_text_color || '#333333',
		         'border-radius': (Item.params.btn_radius || 0) + 'px',
		         'border': (Item.params.btn_border_size || 0) + 'px solid ' + (Item.params.btn_border_color || '#e6e6e6')
		     }">
		    <!-- 当有自定义图标时显示 -->
		    <img ng-if="Item.params.btn_type == 'icon' && Item.params.custom_icon_path" 
		         ng-src="{{Item.params.custom_icon_path}}" 
		         alt="搜索图标" 
		         style="display:inline-block !important;" />
		    
		    <!-- 默认图标 -->
		    <i class="fa fa-search" ng-if="Item.params.btn_type == 'icon' && !Item.params.custom_icon_path"></i>
		    
		    <!-- 文字按钮 -->
		    <span ng-if="Item.params.btn_type == 'text'">{{Item.params.btn_text || '搜索'}}</span>
		</div>
	</div>
	
	<div class="dsn-position-right" ng-if="Item.params.position==2">
	    <span ng-if="Item.params.areamode==0" ng-style="{color:Item.params.area_color}">{{Item.params.areamode_str}}</span>
	    <span ng-if="Item.params.areamode==1" ng-style="{color:Item.params.area_color}">{{Item.params.areamode_str}}</span>
	    <span ng-if="Item.params.areamode==2" ng-style="{color:Item.params.area_color}">{{Item.params.areamode_str}}</span>
	</div>
	
	
	
	
    
</div>









<!--备份-->

<!--<div class="dsn-mod dsn-search" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">-->
<!--	<div ng-show="!Item.params.openmore || Item.params.openmore<=0" class="dp-search-search"-->
<!--		ng-style="{'border-color':Item.params.bordercolor,'border-radius':Item.params.borderradius+'px'}" >-->
<!--		<div class="dp-search-search-f1"></div>-->
<!--		<div class="dp-search-search-f2">-->
<!--			<input class="dp-search-search-input" name="keyword" placeholder="{{Item.params.placeholder}}" ng-style="{color:Item.params.color}" />-->
<!--		</div>-->
        
<!--	</div>-->
    
<!--</div>--></script>
	<script type="text/ng-template" id="show-lbssearch.html"><?php if(getcustom('lbs_search')): ?>
<div class="dsn-mod dsn-search" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
    <div style="display: flex;">
        <div  style="width: 80px;overflow: hidden;height: 36px;line-height: 36px;flex-shrink: 0">
            <select class="layui-input"  style="font-size: 13px;border: 0;height: 36px;line-height: 36px">
                <option>北京</option>
            </select>
        </div>
        <div class="dp-search-search"
            ng-style="{'border-color':Item.params.bordercolor,'border-radius':Item.params.borderradius+'px','width':'100%'}">
            <div class="dp-search-search-f1"></div>
            <div class="dp-search-search-f2">
                <input class="dp-search-search-input" name="keyword" placeholder="{{Item.data?Item.data[0]['title2']:Item.params.placeholder}}" ng-style="{color:Item.params.color}" />
            </div>
        </div>
        <div ng-show="Item.params.btn==1">
            <div style="width: 50px;text-align: center;float: right;line-height:36px;background: #f0f0f0;">
                搜索
            </div>
        </div>
    </div>
</div>
<?php endif; ?></script>
	<script type="text/ng-template" id="show-line.html"><div class="dsn-mod dsn-line" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
    <div class="dsn-line-line" ng-style="{'border-top-width':Item.params.height+'px','border-top-style':Item.params.style,'border-top-color':Item.params.color,'margin-left':Item.params.margin1+'px','margin-right':Item.params.margin1+'px','margin-top':Item.params.margin2+'px','margin-bottom':Item.params.margin2+'px'}"></div>
</div></script>
	<script type="text/ng-template" id="show-blank.html"><div class="dsn-mod dsn-blank" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'height':Item.params.height+'px','background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px'}"></div></script>
	<script type="text/ng-template" id="show-product.html"><div class="dsn-mod dp-product" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px', 'position': 'relative'}">
	<!-- 混合模式CSS样式 -->
	<style>
		.article-item, .video-item {
			background-color: #fff;
			border-radius: 4px;
			overflow: hidden;
			margin-bottom: 15px;
			box-shadow: 0 1px 3px rgba(0,0,0,0.1);
		}
		.article-pic, .video-pic {
			width: 100%;
			height: 0;
			padding-bottom: 56.25%;
			position: relative;
			overflow: hidden;
		}
		.article-pic img, .video-pic img {
			position: absolute;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
		.article-info, .video-info {
			padding: 10px;
		}
		.a-title, .v-title {
			font-size: 16px;
			font-weight: bold;
			margin-bottom: 6px;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
		.a-desc, .v-desc {
			color: #666;
			font-size: 12px;
			margin-bottom: 8px;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
		.a-meta, .v-meta {
			color: #999;
			font-size: 12px;
			display: flex;
			justify-content: space-between;
		}
		
		/* 瀑布流样式 */
		.waterfall-container {
			column-count: 2;
			column-gap: 10px;
		}
		.waterfall-item {
			break-inside: avoid;
			margin-bottom: 10px;
		}
	</style>
	<div ng-if="Item.params.bgimg" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-position: center; background-size: cover; background-repeat: no-repeat; z-index: 1;" ng-style="{'background-image': 'url('+Item.params.bgimg+')'}"></div>
	<div style="position: relative; z-index: 2;">
		<div class="header-titles" style="padding: 15px; display: flex; justify-content: space-between; align-items: center;">
			<a class="more-link" ng-if="Item.params.more_text" ng-href="{{Item.params.hrefurl}}" style="order: 1; display: inline-flex; align-items: center; font-size: 12px; text-decoration: none; padding: 4px 12px; margin-right: 15px; align-self: center; color: {{Item.params.more_text_color || '#666'}}">
				{{Item.params.more_text}}
				<i class="fa fa-angle-right" style="margin-left: 4px;"></i>
			</a>
			<div class="title-area" style="text-align: right; flex: 1; order: 2;">
				<div class="main-title" ng-if="Item.params.main_title" style="font-size: 16px; font-weight: bold; margin-bottom: 4px; color: {{Item.params.main_title_color || '#333'}}">{{Item.params.main_title}}</div>
				<div class="sub-title" ng-if="Item.params.sub_title" style="font-size: 12px; color: {{Item.params.sub_title_color || '#666'}}">{{Item.params.sub_title}}</div>
			</div>
		</div>
		<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
		<!--123排-->
		<div class="dp-product-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3' || Item.params.style=='waterfall'">
			<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 || Item.params.style=='waterfall'  ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}" ng-style="{'background-color':Item.params.probgcolor}">
				<div class="product-pic" ng-style="{'background-color':Item.params.probgcolor}">
					<img class="image" src="{{item.pic}}"/>
					<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info" ng-style="{'background-color':Item.params.probgcolor}">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2">
						<div class="p2-1" ng-if="Item.params.showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)">
							<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
							<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
						</div>
						<div class="p2-1" ng-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0" style="height: 25px;line-height: 22px;">
							<span ng-if="Item.params.style!='1'" class="t1" style="color:<?php echo t('color1'); ?>;font-size: 14px">询价</span>
							<span ng-if="Item.params.style=='1'" class="t1" style="color:<?php echo t('color1'); ?>">询价</span>
							<?php if((getcustom('shop_other_infor'))): ?>
								<div ng-if="item.price_type == 1" style="background:<?php echo t('color1'); ?>;color: #fff;border-radius: 25px 25px;line-height: 25px;text-align: center;font-size: 10px;padding: 0 5px;display: inline-block;float: right;" >
								{{item.xunjia_text?item.xunjia_text:'联系TA'}}
								</div>
							<?php endif; ?>
						</div>
					</div>
					<?php if((getcustom('shop_other_infor'))): ?>
						<div class="p1" ng-if="item.merchant_name" style="color: #666;font-size: 12px;white-space: nowrap;text-overflow: ellipsis;margin-top: 3px;height: 15px;font-weight: normal;">
							<span>{{item.merchant_name}}</span>
						</div>
						<div class="p1" ng-if="item.main_business" style="color: #666;font-size: 12px;margin-top: 2px;font-weight: normal;">
							<span>{{item.main_business}}</span>
						</div>
					<?php endif; ?>
					<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已售{{item.sales}}件</div>
					<!-- <div ng-if="Item.params.showsales=='1' && item.sales>0" style="height: 22px;"></div> -->
					<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1 && !item.price_type"><span class="iconfont icon_gouwuche"></span></div>
					<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2 && !item.price_type"><img src="{{Item.params.cartimg}}" class="img"/></div>
				</div>
			</div>
		</div>
		
		<!-- 混合模式展示 -->
		<div class="dp-product-item" ng-show="Item.params.style=='mixed'" ng-class="{'waterfall-container': Item.params.mixed_layout=='waterfall'}">
			<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="!Item.mixed_items || Item.mixed_items.length == 0">
				混合模式暂无数据，请添加商品、文章或视频...
			</div>
			<div 
				ng-repeat="item in Item.mixed_items" 
				ng-init="index = $index" 
				ng-class="{'waterfall-item': Item.params.mixed_layout=='waterfall'}"
				ng-style="{'background-color':Item.params.probgcolor}"
				style="{{Item.params.mixed_layout=='waterfall' ? '' : (Item.params.mixed_layout==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0)+';float:left;' : 'width:100%;margin-bottom:10px;')}}"
			>
				
				<!-- 空数据提示 -->
				<div ng-if="item.type == 'empty'" style="padding: 20px; text-align: center; color: #999;">
					<div style="font-size: 14px; margin-bottom: 10px;">{{item.name}}</div>
					<div style="font-size: 12px;">{{item.subname}}</div>
				</div>
				
				<!-- 商品展示 -->
				<div ng-if="item.type == 'product'" class="product-item">
					<div class="product-pic" ng-style="{'background-color':Item.params.probgcolor}">
						<img class="image" src="{{item.pic}}"/>
						<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
					</div>
					<div class="product-info" ng-style="{'background-color':Item.params.probgcolor}">
						<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
						<div class="p2">
							<div class="p2-1" ng-if="Item.params.showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)">
								<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
								<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
							</div>
							<div class="p2-1" ng-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0" style="height: 25px;line-height: 22px;">
								<span ng-if="Item.params.style!='1'" class="t1" style="color:<?php echo t('color1'); ?>;font-size: 14px">询价</span>
								<span ng-if="Item.params.style=='1'" class="t1" style="color:<?php echo t('color1'); ?>">询价</span>
								<?php if((getcustom('shop_other_infor'))): ?>
									<div ng-if="item.price_type == 1" style="background:<?php echo t('color1'); ?>;color: #fff;border-radius: 25px 25px;line-height: 25px;text-align: center;font-size: 10px;padding: 0 5px;display: inline-block;float: right;" >
									{{item.xunjia_text?item.xunjia_text:'联系TA'}}
									</div>
								<?php endif; ?>
							</div>
						</div>
						<?php if((getcustom('shop_other_infor'))): ?>
							<div class="p1" ng-if="item.merchant_name" style="color: #666;font-size: 12px;white-space: nowrap;text-overflow: ellipsis;margin-top: 3px;height: 15px;font-weight: normal;">
								<span>{{item.merchant_name}}</span>
							</div>
							<div class="p1" ng-if="item.main_business" style="color: #666;font-size: 12px;margin-top: 2px;font-weight: normal;">
								<span>{{item.main_business}}</span>
							</div>
						<?php endif; ?>
						<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已售{{item.sales}}件</div>
						<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1 && !item.price_type"><span class="iconfont icon_gouwuche"></span></div>
						<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2 && !item.price_type"><img src="{{Item.params.cartimg}}" class="img"/></div>
					</div>
				</div>
				
				<!-- 文章展示 -->
				<div ng-if="item.type == 'article'" class="article-item">
					<div class="article-pic">
						<img ng-src="{{item.pic}}" alt="{{item.name}}">
					</div>
					<div class="article-info">
						<div class="a-title">{{item.name}}</div>
						<div class="a-desc" ng-if="item.subname">{{item.subname}}</div>
						<div class="a-meta">
							<span class="a-author" ng-if="item.author">{{item.author}}</span>
							<span class="a-time" ng-if="item.createtime">{{item.createtime}}</span>
							<span class="a-views" ng-if="item.views">浏览 {{item.views}}</span>
						</div>
					</div>
				</div>
				
				<!-- 短视频展示 -->
				<div ng-if="item.type == 'video'" class="video-item">
					<div class="video-pic" style="position: relative;">
						<img ng-src="{{item.coverimg}}" alt="{{item.name}}">
						<div class="video-play-icon" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #fff; font-size: 40px;">
							<i class="fa fa-play-circle"></i>
						</div>
					</div>
					<div class="video-info">
						<div class="v-title">{{item.name}}</div>
						<div class="v-desc" ng-if="item.description">{{item.description}}</div>
						<div class="v-meta">
							<span class="v-author" ng-if="Item.params.showlogo == 1 && item.merchant_name">{{item.merchant_name}}</span>
							<span class="v-views" ng-if="Item.params.showviewnum == 1 && item.view_num">{{item.view_num}}次播放</span>
							<span class="v-likes" ng-if="Item.params.showzannum == 1 && item.zan_num">{{item.zan_num}}赞</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="dp-product-itemlist" ng-show="Item.params.style=='list'">
			<div class="item" ng-repeat="item in Item.data" ng-style="{'background-color':Item.params.probgcolor}">
				<div class="product-pic" ng-style="{'background-color':Item.params.probgcolor}">
					<img class="image" src="{{item.pic}}" mode="widthFix"/>
					<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2" ng-if="Item.params.showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)" style="height: 25px;line-height: 22px;">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px">￥</span>{{item.sell_price}}</span>
						<span class="t2" ng-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
					<div class="p2" ng-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0">
						<span class="t1" style="color:<?php echo t('color1'); ?>;font-size: 14px">询价</span>
						<?php if((getcustom('shop_other_infor'))): ?>
							<div ng-if="item.price_type == 1" style="background:<?php echo t('color1'); ?>;color: #fff;border-radius: 25px 25px;line-height: 25px;text-align: center;font-size: 10px;padding: 0 5px;display: inline-block;float: right;" >
							{{item.xunjia_text?item.xunjia_text:'联系TA'}}
							</div>
						<?php endif; ?>
					</div>
					<?php if((getcustom('shop_other_infor'))): ?>
					<div class="p1" ng-if="item.merchant_name" style="color: #666;font-size: 12px;white-space: nowrap;text-overflow: ellipsis;margin-top: 3px;height: 15px;font-weight: normal;"><span>{{item.merchant_name}}</span></div>
					<div class="p1" ng-if="item.main_business" style="color: #666;font-size: 12px;margin-top: 2px;font-weight: normal;"><span>{{item.main_business}}</span></div>
					<?php endif; ?>
					<div class="p3">
						<div class="p3-1" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已售{{item.sales}}件</span></div>
					</div>
					<div ng-if="Item.params.showsales!='1' || item.sales<=0" style="height: 22px;"></div>
					<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1 && !item.price_type"><span class="iconfont icon_gouwuche"></span></div>
					<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2 && !item.price_type"><img src="{{Item.params.cartimg}}" class="img"/></span></div>
				</div>
			</div>
		</div> 
		<div class="dp-product-itemline" ng-show="Item.params.style=='line'">
			<div class="item" ng-repeat="item in Item.data" ng-style="{'background-color':Item.params.probgcolor}" >
				<div class="product-pic" ng-style="{'background-color':Item.params.probgcolor}">
					<img class="image" src="{{item.pic}}"/>
					<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2">
						<div class="p2-1" ng-if="Item.params.showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)" style="height: 25px;line-height: 22px;">
							<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
							<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
						</div>
						<div class="p2-1" ng-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0">
							<span class="t1" style="color:<?php echo t('color1'); ?>;font-size: 14px">询价</span>
							<?php if((getcustom('shop_other_infor'))): ?>
								<div ng-if="item.price_type == 1" style="background:<?php echo t('color1'); ?>;color: #fff;border-radius: 25px 25px;line-height: 25px;text-align: center;font-size: 10px;padding: 0 5px;display: inline-block;float: right;" >
								{{item.xunjia_text?item.xunjia_text:'联系TA'}}
								</div>
							<?php endif; ?>
						</div>
					</div>
					<?php if((getcustom('shop_other_infor'))): ?>
					<div class="p1" ng-if="item.merchant_name" style="color: #666;font-size: 12px;white-space: nowrap;text-overflow: ellipsis;margin-top: 3px;height: 15px;font-weight: normal;"><span>{{item.merchant_name}}</span></div>
					<div class="p1" ng-if="item.main_business" style="color: #666;font-size: 12px;margin-top: 2px;font-weight: normal;"><span>{{item.main_business}}</span></div>
					<?php endif; ?>
					<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已售{{item.sales}}件</div>
					<div ng-if="Item.params.showsales!='1' || item.sales<=0" style="height: 22px;"></div>
					<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1 && !item.price_type"><span class="iconfont icon_gouwuche"></span></div>
					<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2 && !item.price_type"><img src="{{Item.params.cartimg}}" class="img"/></div>
					
				</div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-cycle.html"><div class="dsn-mod dp-collage" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
	<!--123排-->
	<div class="dp-collage-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
		<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3">
					<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">{{item.pspl}}</div>
					<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已售{{item.sales}}件</span></div>
				</div>
			</div>
		</div>
	</div>
	<div class="dp-collage-itemlist" ng-show="Item.params.style=='list'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}" mode="widthFix"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2" ng-if="Item.params.showprice != '0'">
					<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px">￥</span>{{item.sell_price}}</span>
					<span class="t2" v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
				</div>
				<div class="p3">
					<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">{{item.pspl}}</div>
					<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已售{{item.sales}}件</span></div>
				</div>
			</div>
		</div>
	</div>
	<div class="dp-collage-itemline" ng-show="Item.params.style=='line'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>

			</div>
		</div>
	</div>
</div>
</script>
	<script type="text/ng-template" id="show-collage.html"><div class="dsn-mod dp-collage" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
	<!--123排-->
	<div ng-show="Item.params.shopstyle=='1' || !Item.params.shopstyle" style="width: 100%;">
		<div class="dp-collage-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
			<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}"/>
					<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2">
						<div class="p2-1" ng-if="Item.params.showprice != '0'">
							<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
							<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
						</div>
					</div>
					<div class="p3">
						<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">{{item.teamnum}}人团</div>
						<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已拼成{{item.sales}}件</span></div>
					</div>
				</div>
			</div>
		</div>
		<div class="dp-collage-itemlist" ng-show="Item.params.style=='list'">
			<div class="item" ng-repeat="item in Item.data">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}" mode="widthFix"/>
					<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px">￥</span>{{item.sell_price}}</span>
						<span class="t2" v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
					<div class="p3">
						<div class="p3-1">{{item.teamnum}}人团</div>
						<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已拼成{{item.sales}}件</span></div>
					</div>
				</div>
			</div>
		</div>
		<div class="dp-collage-itemline" ng-show="Item.params.style=='line'">
			<div class="item" ng-repeat="item in Item.data">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}"/>
					<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2">
						<div class="p2-1" ng-if="Item.params.showprice != '0'">
							<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
							<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
						</div>
					</div>
					<div class="p3">
						<div class="p3-1">{{item.teamnum}}人团</div>
						<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已拼成{{item.sales}}件</span></div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
</div></script>
	<script type="text/ng-template" id="show-kanjia.html"><div class="dsn-mod dp-collage" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
	<!--123排-->
	<div class="dp-collage-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
		<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.min_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1'">￥{{item.sell_price}}</span>
					</div>
				</div>
				<div class="p3">
					<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">砍价</div>
					<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已砍走{{item.sales}}件</span></div>
				</div>
			</div>
		</div>
	</div>
	<div class="dp-collage-itemlist" ng-show="Item.params.style=='list'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}" mode="widthFix"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2" ng-if="Item.params.showprice != '0'">
					<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px">￥</span>{{item.min_price}}</span>
					<span class="t2" v-if="showprice == '1'">￥{{item.sell_price}}</span>
				</div>
				<div class="p3">
					<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">砍价</div>
					<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已砍走{{item.sales}}件</span></div>
				</div>
			</div>
		</div>
	</div>
	<div class="dp-collage-itemline" ng-show="Item.params.style=='line'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.min_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1'">￥{{item.sell_price}}</span>
					</div>
				</div>
				<div class="p3">
					<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">砍价</div>
					<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已砍走{{item.sales}}件</span></div>
				</div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-seckill.html"><div class="dsn-mod dp-collage" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
	<!--123排-->
	<?php if(getcustom('design_seckill')): ?>
	<div ng-show="Item.params.shopstyle=='2'" style="width: 100%;">
		<div ng-show="Item.params.showtitle=='1'" style="width: 100%;">
			<div ng-show="Item.params.titlestyle=='1'" class="dp-time">
				<img src="<?php echo PRE_URL; ?>/static/imgsrc/decoration_crush.png" alt="" class="dp-time-back"/>
				<div class="dp-time-content">
					<div class="dp-time-title">限时秒杀</div>
					<div class="dp-time-text">
						距离结束<div class="dp-time-time">09</div>:<div class="dp-time-time">09</div>:<div class="dp-time-time">09</div>
					</div>
				</div>
			</div>
			<div ng-show="Item.params.titlestyle=='2'" class="dp-bTime">
				<img src="<?php echo PRE_URL; ?>/static/imgsrc/decoration_crush.png" alt="" class="dp-bTime-back"/>
				<div class="dp-bTime-content">
					<div class="dp-bTime-title">限时秒杀</div>
					<div class="dp-bTime-text">
						距离结束<div class="dp-bTime-time">09</div>:<div class="dp-bTime-time">09</div>:<div class="dp-bTime-time">09</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<?php endif; ?>
	
	<div ng-show="Item.params.shopstyle=='1' || !Item.params.shopstyle" style="width: 100%;">
		<div class="dp-collage-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
			<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}"/>
					<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2">
						<div class="p2-1" ng-if="Item.params.showprice != '0'">
							<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
							<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
						</div>
					</div>
					<div class="p4" ng-if="Item.params.showtime == 1 && Item.params.style!='3'">
						<div ng-if="item.seckill_status == 2">活动已结束</div>
						<div ng-if="item.seckill_status == 1" class="flex-row"><div class="h24">还剩余</div><div class="flex1"></div><div><span>{{item.hour}}</span>:<span>{{item.minute}}</span>:<span>{{item.second}}</span></div></div>
						<div ng-if="item.seckill_status == 0" class="flex-row"><div class="h24">距开抢</div><div class="flex1"></div><div><span>{{item.hour}}</span>:<span>{{item.minute}}</span>:<span>{{item.second}}</span></div></div>
					</div>
					<div class="p3">
						<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">秒杀</div>
						<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已抢{{item.sales}}件</span></div>
					</div>
				</div>
			</div>
		</div>
		<div class="dp-collage-itemlist" ng-show="Item.params.style=='list'">
			<div class="item" ng-repeat="item in Item.data">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}" mode="widthFix"/>
					<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px">￥</span>{{item.sell_price}}</span>
						<span class="t2" v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
					<div class="p4" ng-if="Item.params.showtime == 1">
						<div ng-if="item.seckill_status == 2">活动已结束</div>
						<div ng-if="item.seckill_status == 1" class="flex-row"><div class="h24">距活动结束</div><div class="flex1"></div>
							<div ng-if="item.day > 0"><span>{{item.day}}</span>天<span>{{item.day_hour}}</span>:<span>{{item.minute}}</span>:<span>{{item.second}}</span></div>
							<div ng-else><span>{{item.hour}}</span>:<span>{{item.minute}}</span>:<span>{{item.second}}</span></div>
						</div>
						<div ng-if="item.seckill_status == 0" class="flex-row"><div class="h24">距活动开始</div><div class="flex1"></div>
							<div ng-if="item.day > 0"><span>{{item.day}}</span>天<span>{{item.day_hour}}</span>:<span>{{item.minute}}</span>:<span>{{item.second}}</span></div>
							<div ng-else><span>{{item.hour}}</span>:<span>{{item.minute}}</span>:<span>{{item.second}}</span></div>
						</div>
					</div>
					<div class="p3">
						<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">秒杀</div>
						<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已抢{{item.sales}}件</span></div>
					</div>
				</div>
			</div>
		</div>
		<div class="dp-collage-itemline" ng-show="Item.params.style=='line'">
			<div class="item" ng-repeat="item in Item.data">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}"/>
					<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="p2">
						<div class="p2-1" ng-if="Item.params.showprice != '0'">
							<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
							<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
						</div>
					</div>
					<div class="p3">
						<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">秒杀</div>
						<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已抢{{item.sales}}件</span></div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 
		shopstyle商品风格 风格一(1) 风格二(2)
		showtitle标题倒计时 显示(1) 不显示(2)
		titlestyle标题风格 风格一(1) 风格二(2)
	-->
	<?php if(getcustom('design_seckill')): ?>
	<div ng-show="Item.params.shopstyle=='2'" style="width: 100%;">
		<div class="dp-collage-item" ng-show="Item.params.style=='2'">
			<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}"/>
					<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div class="rate" ng-if="Item.params.style=='2'">
						<img src="<?php echo PRE_URL; ?>/static/imgsrc/decoration_rate.png"/>
						<span class="text">仅剩1件</span>
					</div>
					<div class="p5">原价:<span class="text">￥{{item.market_price}}</span></div>
					<div class="price" style="color:<?php echo t('color1'); ?>">
						<span class="price_text">￥{{item.sell_price}}</span>
						<div class="price_add" style="background:<?php echo t('color1'); ?>">+</div>
					</div>
				</div>
			</div>
		</div>
		<div class="dp-collage-itemlist" ng-show="Item.params.style=='list'">
			<div class="item" ng-repeat="item in Item.data">
				<div class="product-pic product-pic1">
					<img class="image" src="{{item.pic}}" mode="widthFix"/>
					<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p5" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<!-- <div class="p6">平价实用</div> -->
					<div class="rate"><img width="100" src="<?php echo PRE_URL; ?>/static/imgsrc/decoration_rate.png"/><span>仅剩1件</span></div>
					<div ng-if="Item.params.showprice != '0'">
						<div class="p7">原价:<span>{{item.market_price}}</span></div>
						<div v-if="showprice == '1' && item.market_price*1 > item.sell_price*1" style="color:<?php echo t('color1'); ?>" class="p8">￥{{item.sell_price}}</div>
					</div>
					<div style="background:<?php echo t('color1'); ?>" class="btn">马上抢</div>
				</div>
			</div>
		</div>
		<div class="dp-collage-itemline1" ng-show="Item.params.style=='line'">
			<div class="item" ng-repeat="item in Item.data">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}"/>
					<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
					<div class="tag" style="background:<?php echo t('color1'); ?>">秒杀</div>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<div style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>" class="surplus">剩余1件</div>
					<div class="price" style="color:<?php echo t('color1'); ?>">
						<span class="price_text">￥{{item.sell_price}}</span>
						<div class="price_add" style="background:<?php echo t('color1'); ?>">+</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<?php endif; ?>
</div></script>
	<script type="text/ng-template" id="show-scoreshop.html"><div class="dsn-mod dp-socreshop" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
	<!--123排-->
	<div class="dp-socreshop-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
		<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>">{{item.score_price}}<?php echo t('积分'); ?><text ng-if="item.money_price>0">+{{item.money_price}}元</text></span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已兑换{{item.sales}}件</div>
			</div>
		</div>
	</div>
	<div class="dp-socreshop-itemlist" ng-show="Item.params.style=='list'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}" mode="widthFix"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2" ng-if="Item.params.showprice != '0'">
					<span class="t1" style="color:<?php echo t('color1'); ?>">{{item.score_price}}<?php echo t('积分'); ?><text ng-if="item.money_price>0">+{{item.money_price}}元</text></span>
					<span class="t2" ng-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
				</div>
				<div class="p3">
					<div class="p3-1" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已兑换{{item.sales}}件</span></div>
				</div>
			</div>
		</div>
	</div>
	<div class="dp-socreshop-itemline" ng-show="Item.params.style=='line'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>">{{item.score_price}}<?php echo t('积分'); ?><text ng-if="item.money_price>0">+{{item.money_price}}元</text></span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已兑换{{item.sales}}件</div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-coupon.html"><div class="dsn-mod dsn-coupon" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height:60px; text-align: center; color: #999; font-size: 16px;" ng-show="Item.data == ''">暂无<?php echo t('优惠券'); ?>...</div>
	<div class="couponlist">
		<div class="coupon" ng-repeat="item in Item.data" style="background:url('{{Item.params.bgpic}}');background-size:100%">
			<div class="f1" ng-show="item.type==1" style="color:{{Item.params.titlecolor}}"><span style="font-size:10px">￥</span>{{item.money}}<span style="font-size:12px"> <?php echo t('优惠券'); ?></span></div>
			<div class="f1" ng-show="item.type!=1" style="color:{{Item.params.titlecolor}}"><span style="font-size:14px">{{item.name}}</span></div>
			<div class="f2" style="color:{{Item.params.remarkcolor}}" ng-show="item.type==1 && item.minprice!=0">满{{item.minprice}}元可用</div>
			<div class="f2" style="color:{{Item.params.remarkcolor}}" ng-show="item.type==1 && item.minprice==0">无门槛</div>
			<div class="f2" style="color:{{Item.params.remarkcolor}}" ng-show="item.type==2">礼品券</div>
			<div class="f2" style="color:{{Item.params.remarkcolor}}" ng-show="item.type==3">计次券</div>
			<div class="f2" style="color:{{Item.params.remarkcolor}}" ng-show="item.type==4">运费抵扣券</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-richtext.html"><div class="dsn-mod dsn-richtext" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div string-html="Item.content"></div>
	<div ng-show="!Item.content">
		<p><span style="font-size: 20px;">这里是『富文本』区域</span></p>
		<p>你可以对文字进行<strong>加粗</strong>、<em>斜体</em>、<span style="text-decoration: underline;">下划线</span>、<span style="text-decoration: line-through;">删除线</span>、文字<span style="color: rgb(0, 176, 240);">颜色</span>、<span style="background-color: rgb(255, 192, 0); color: rgb(255, 255, 255);">背景色</span>、以及字号<span style="font-size: 20px;">大</span><span style="font-size: 14px;">小</span>等简单排版操作。
		</p>
		<p>也可在这里插入图片</p>
		<p>&nbsp;</p>
		<p>&nbsp;</p>
		<p>&nbsp;</p>
	</div>
</div></script>
	<script type="text/ng-template" id="show-cube.html"><!-- <div class="dsn-mod dsn-cube" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor}">
    <div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;" ng-if="!hasCube(Item)">未设置魔方</div>
    <div class="inner">
        <table>
            <tr ng-repeat="row in Item.params.layout" ng-init="rowindex = $index">
                <td ng-init="colindex = $index" ng-repeat="col in row" class="{{col.classname}} rows-{{col.rows}} cols-{{col.cols}}" 
                    ng-class="{'empty' : col.isempty, 'not-empty' : !col.isempty}" 
                    rowspan="{{col.rows}}" 
                    colspan="{{col.cols}}">
                    <div ng-if="!col.isempty && col.imgurl"><a href="{{col.hrefurl}}"><img ng-src="{{col.imgurl}}" style="width: 100%;height: auto;" /></a></div>
                </td>
            </tr> 
        </table>
    </div>
</div>
 -->

<div class="dsn-mod dsn-cube" ng-class="{'dsn-mod-select':Item.id == focus}" style="display:block;position:relative;background:{{Item.params.bgcolor}};height:{{Item.params.maxheight*85 - Item.params.margin_y*2}}px;margin:{{Item.params.margin_y}}px {{Item.params.margin_x}}px 0;padding:{{Item.params.padding_y}}px {{Item.params.padding_x}}px">
  <div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;" ng-if="!hasCube(Item)">未设置魔方</div>
	<div ng-if="!show">
		<span ng-repeat="(idx,row) in Item.params.layout">
			<span ng-repeat="(idx2,col) in row">
				<div ng-show="!col.isempty" style="position:absolute;top:{{idx*(340-Item.params.margin_y*2-Item.params.padding_y*2)/4-3+Item.params.padding_y*1}}px;left:{{idx2*(340-Item.params.margin_x*2-Item.params.padding_x*2)/4-2+Item.params.padding_x*1}}px;width:{{col.cols*(340-Item.params.margin_x*2-Item.params.padding_x*2)/4}}px;height:{{col.rows*(340-Item.params.margin_y*2-Item.params.padding_y*2)/4}}px;line-height:{{col.rows*(340-Item.params.margin_y*2-Item.params.padding_y*2)/4}}px;"><img src="{{col.imgurl}}" style="width:100%;max-height:100%;"/></div>
			</span>
		</span>
	</div>
	<div ng-if="show">
		<span ng-repeat="(idx,row) in Item.params.layout">
			<span ng-repeat="(idx2,col) in row">
				<div ng-show="!col.isempty" style="position:absolute;top:{{idx*(340-Item.params.margin_y*2-Item.params.padding_y*2)/4+Item.params.padding_y*1}}px;left:{{idx2*(340-Item.params.margin_x*2-Item.params.padding_x*2)/4+Item.params.padding_x*1}}px;width:{{col.cols*(340-Item.params.margin_x*2-Item.params.padding_x*2)/4}}px;height:{{col.rows*(340-Item.params.margin_y*2-Item.params.padding_y*2)/4}}px;line-height:{{col.rows*(340-Item.params.margin_y*2-Item.params.padding_y*2)/4}}px;"><img src="{{col.imgurl}}" style="width:100%;max-height:100%;"/></div>
			</span>
		</span>
	</div>
</div>
 </script>
	<script type="text/ng-template" id="show-video.html"><div class="dsn-mod dsn-video" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'height':Item.params.height,'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px','background-image':'url('+Item.params.bg_image+')','background-size':'cover','background-position':'center center','background-repeat':'no-repeat','border-radius':(Item.params.bg_border_radius || 0)+'px'}">
		<a href="{{Item.params.src|| 'javascript:void(0);'}}">
				<div style="position:relative;width:100%;height:auto;border-radius:{{(Item.params.border_radius || 0)+'px'}};overflow:hidden">
					<img ng-src="{{Item.params.pic}}" style="min-height:200px;background:#999;width:100%"/>
					<div style="position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.7)"><div style="width:100%;height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;color:#fff;"><i class="fa fa-play" style="font-size:20px"></i><div style="margin-top:5px;font-size:16px">00:00</div></div></div>
				</div>
		</a>
</div></script>
	<script type="text/ng-template" id="show-article.html"><div class="dsn-mod dp-article" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;" ng-show="Item.data == ''">一篇文章都没有...</div>
	<!--单排-->
	<div ng-show="Item.params.style=='1'" ng-repeat="item in Item.data" class="article-item1">
		<div class="article-pic" ng-if="Item.params.showpic==1">
			<img class="image" src="{{item.pic}}" style="{{Item.params.pic_width>0&&Item.params.pic_height>0?'width: '+Item.params.pic_width+'px;height: '+Item.params.pic_height+'px':''}}"/>
		</div>
		<div class="article-info" >
			<div class="p1" style="{{Item.params.title_size>0?'font-size: '+Item.params.title_size+'px':''}}">{{item.name}}</div>
			
			<div class="p2">
				<span style="overflow:hidden" class="flex1" ng-if="Item.params.showtime==1">{{item.createtime}}</span>
				<span style="overflow:hidden" ng-if="Item.params.showreadcount==1">阅读 {{item.readcount}}</span>
			</div>
		</div>
	</div>
	<!--双排-->
	<div ng-show="Item.params.style=='2' || Item.params.style=='6'" ng-repeat="item in Item.data" class="article-item2" ng-init="index = $index" style="margin-right:{{index%2==0?'2%':'0'}}">
		<div class="article-pic" ng-if="Item.params.style=='6' || Item.params.showpic==1">
			<img class="image" src="{{item.pic}}" style="{{Item.params.pic_width>0&&Item.params.pic_height>0?'width: '+Item.params.pic_width+'px;height: '+Item.params.pic_height+'px':''}}"/>
		</div>
		<div class="article-info" >
			<div class="p1" style="{{Item.params.title_size>0?'font-size: '+Item.params.title_size+'px':''}}">{{item.name}}</div>
			
			<div class="p2">
				<span style="overflow:hidden" class="flex1" ng-if="Item.params.showtime==1">{{item.createtime}}</span>
				<span style="overflow:hidden" ng-if="Item.params.showreadcount==1">阅读 {{item.readcount}}</span>
			</div>
		</div>
	</div>
	<!--左图-->
	<div ng-show="Item.params.style=='4'" ng-repeat="item in Item.data" class="article-itemlist">
		<div class="article-pic" ng-if="Item.params.showpic==1">
			<img class="image" src="{{item.pic}}" style="{{Item.params.pic_width>0&&Item.params.pic_height>0?'width: '+Item.params.pic_width+'px;height: '+Item.params.pic_height+'px':''}}"/>
		</div>
		<div class="article-info" >
			<div class="p1" style="{{Item.params.title_size>0?'font-size: '+Item.params.title_size+'px':''}}">{{item.name}}</div>
			
			<div class="p2">
				<span style="overflow:hidden" class="flex1" ng-if="Item.params.showtime==1">{{item.createtime}}</span>
				<span style="overflow:hidden" ng-if="Item.params.showreadcount==1">阅读 {{item.readcount}}</span>
			</div>
		</div>
	</div>
	<!--右图-->
	<div ng-show="Item.params.style=='5'" ng-repeat="item in Item.data" class="article-itemlist">
		<div class="article-info" style="padding-left:5px;">
			<div class="p1" style="{{Item.params.title_size>0?'font-size: '+Item.params.title_size+'px':''}}">{{item.name}}</div>
			
			<div class="p2">
				<span style="overflow:hidden" class="flex1" ng-if="Item.params.showtime==1">{{item.createtime}}</span>
				<span style="overflow:hidden" ng-if="Item.params.showreadcount==1">阅读 {{item.readcount}}</span>
			</div>
		</div>
		<div class="article-pic" ng-if="Item.params.showpic==1">
			<img class="image" src="{{item.pic}}"/>
		</div>
	</div>
	<!--三排-->
	<div ng-show="Item.params.style=='7'" ng-repeat="item in Item.data" class="article-item2" ng-init="index = $index" style="margin-right:{{(index+1)%3==0?'0':'2%'}};width: 32%;">
		<div class="article-info">
    		<div class="p1" style="{{Item.params.title_size>0?'font-size: '+Item.params.title_size+'px':''}}">{{item.name}}</div>
    	</div>
    	
		<div class="article-pic" ng-if="Item.params.showpic==1">
			<img class="image" src="{{item.pic}}" style="{{Item.params.pic_width>0&&Item.params.pic_height>0?'width: '+Item.params.pic_width+'px;height: '+Item.params.pic_height+'px':''}}"/>
		</div>
		<div class="article-info">
			<div class="p2">
				<span style="overflow:hidden" class="flex1" ng-if="Item.params.showtime==1">{{item.createtime}}</span>
				<span style="overflow:hidden" ng-if="Item.params.showreadcount==1">阅读 {{item.readcount}}</span>
			</div>
		</div>
	</div>
	
</div></script>
	<script type="text/ng-template" id="show-form.html"><div class="dsn-mod dsn-form" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'color':Item.params.color,'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px','font-size':Item.params.fontsize+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;" ng-if="Item.data == ''">没有表单信息...</div>
	
  <div class="{{Item.params.style==1?'item':'item2'}}" ng-repeat="(idx,item) in Item.data.content" style="border-color:{{Item.params.linecolor}}" ng-if="Item.params.isquery!=1 || item.query == 1">
    <span class='label'>{{item.val1}}<span ng-if="item.val3==1 && Item.params.showmuststar && Item.params.isquery!=1" style="color:red"> *</span></span>
		<input ng-if="item.key=='input'" type="text" name="form{{idx}}" class="input" placeholder="{{item.val2}}" style="background-color:{{Item.params.inputbgcolor}};border-color:{{Item.params.inputbordercolor}}"/>
		<textarea ng-if="item.key=='textarea'" name="form{{idx}}" class='textarea' placeholder="{{item.val2}}" style="background-color:{{Item.params.inputbgcolor}};border-color:{{Item.params.inputbordercolor}}"></textarea>
		<div class="radio1" ng-if="item.key=='radio'" style="flex-wrap:wrap">
			<div ng-repeat="item1 in item.val2" class="radio2">
				<div class="myradio"></div>{{item1}}<span style="padding-left:10px"></span>
			</div>
		</div>
		<div class="checkbox1" ng-if="item.key=='checkbox'" style="flex-wrap:wrap">
			<div ng-repeat="item1 in item.val2" class='checkbox2'>
				<div class="mycheckbox"></div>{{item1}}<span style="padding-left:10px"></span>
			</div>
		</div>
		<div ng-if="item.key=='switch'" class="layui-unselect layui-form-switch" lay-skin="_switch"><em></em><i></i></div>
	
		<picker ng-if="item.key=='selector'" mode="selector" name="form{{idx}}" value="" class='xyy-pic' range="{{item.val2}}" bindchange="bindPickerChange" data-idx="{{idx}}">
				<div class="picker" ng-if="item.val2[formdata[idx]]">当前选择: {{item.val2[formdata[idx]]}}</div>
				<div wx:else>请选择</div>
		</picker>
		<picker ng-if="item.key=='time'" mode="time" name="form{{idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" class="xyy-pic" range="{{item.val2}}" bindchange="bindPickerChange" data-idx="{{idx}}">
				<div class="picker" ng-if="formdata[idx]">当前选择: {{formdata[idx]}}</div>
				<div wx:else>请选择</div>
		</picker>
		<picker ng-if="item.key=='date'" mode="date" name="form{{idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" class="xyy-pic" range="{{item.val2}}" bindchange="bindPickerChange" data-idx="{{idx}}">
				<div class="picker" ng-if="formdata[idx]">当前选择: {{formdata[idx]}}</div>
				<div wx:else>请选择</div>
		</picker>
		<picker ng-if="item.key=='region'" mode="region" name="form{{idx}}" value="" class="xyy-pic" bindchange="bindPickerChange" data-idx="{{idx}}">
				<div class="picker" ng-if="formdata[idx]">当前选择: {{formdata[idx]}}</div>
				<div wx:else>请选择省市区</div>
		</picker>
		<div ng-if="item.key=='upload'" style="margin-left:10px">
				<img src="/static/imgsrc/upload.png" style="width:60px;height:60px"/>
		</div>
		<div ng-if="item.key=='upload_file'" style="margin-left:10px">
				<img src="/static/imgsrc/upload.png" style="width:60px;height:60px"/>
		</div>
	</div>
	<div class="item" ng-if="Item.data.payset==1 && Item.params.isquery!=1">
			<span class='label'>支付金额</span>
			<input type="text" class="input" name="money" value='{{Item.data.price}}' ng-if="Item.data.priceedit==1"/>
			<span class="" ng-if="Item.data.priceedit==0">{{Item.data.price}}</span>
			<span style="padding-left:5px">元</span>
	</div>
	<div ng-if="Item.data != ''" class="form-btn" style="background-color:{{Item.params.btnbgcolor}};border:1px solid {{Item.params.btnbordercolor}};font-size:{{Item.params.btnfontsize}}px;color:{{Item.params.btncolor}};width:{{Item.params.btnwidth}}px;height:{{Item.params.btnheight}}px;line-height:{{Item.params.btnheight}}px;border-radius:{{Item.params.btnradius}}px">{{Item.params.btntext}}</div>
</div></script>
	<script type="text/ng-template" id="show-map.html"><div class="dsn-mod dsn-map" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
    <div id="showmp_{{Item.id}}" style="width:100%;height:{{Item.params.height}}px"></div>
</div></script>
	<script type="text/ng-template" id="show-button.html"><div class="dsn-mod dsn-button" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px','font-size':Item.params.fontsize+'px'}">
	<div class="form-btn" style="background-color:{{Item.params.btnbgcolor}};border:1px solid {{Item.params.btnbordercolor}};font-size:{{Item.params.btnfontsize}}px;color:{{Item.params.btncolor}};width:{{Item.params.btnwidth}}px;height:{{Item.params.btnheight}}px;line-height:{{Item.params.btnheight}}px;border-radius:{{Item.params.btnradius}}px">{{Item.params.btntext}}</div>
</div></script>
	<script type="text/ng-template" id="show-cover.html"><div ng-if="!show">
	<div class="dsn-mod dsn-cover" ng-click="setfocus(Item.id,$event)" style="cursor:move;color:{{Item.params.color}};background-color:{{Item.params.bgcolor}};width:{{Item.params.width}}px;height:{{Item.params.height}}px;margin:{{Item.params.margin_y}}px {{Item.params.margin_x}}px;padding:{{Item.params.padding_y}}px {{Item.params.padding_x}}px;font-size:{{Item.params.fontsize}}px;border:{{Item.params.border}}px solid {{Item.params.bordercolor}};border-radius:{{Item.params.radius}}px;">
		<span ng-show="Item.params.style==1" style="padding:0 2px">{{Item.params.text}}</span>
		<img ng-show="Item.params.style==2" src="{{Item.params.pic}}" style="width:{{Item.params.picwidth}}px;height:{{Item.params.picheight}}px;"/>
	</div>
</div>

<div ng-if="show">
	<div class="dsn-mod dsn-cover" style="position:fixed;z-index:99999;top:{{Item.params.top*5.6}}px;left:{{Item.params.left*3.4}}px;color:{{Item.params.color}};background-color:{{Item.params.bgcolor}};width:{{Item.params.width}}px;height:{{Item.params.height}}px;margin:{{Item.params.margin_y}}px {{Item.params.margin_x}}px;padding:{{Item.params.padding_y}}px {{Item.params.padding_x}}px;font-size:{{Item.params.fontsize}}px;border:{{Item.params.border}}px solid {{Item.params.bordercolor}};border-radius:{{Item.params.radius}}px;">
		<span ng-show="Item.params.style==1" style="padding:0 2px">{{Item.params.text}}</span>
		<img ng-show="Item.params.style==2" src="{{Item.params.pic}}" style="width:{{Item.params.picwidth}}px;height:{{Item.params.picheight}}px;"/>
	</div>
</div></script>
	<script type="text/ng-template" id="show-business.html"><div class="dsn-mod dsn-business" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商家都没有...</div>
	<div class="buslist">
		<div ng-repeat="item in Item.data" ng-init="idx = $index" class="busbox">
			<div class="businfo">
				<div class="f1"><img src="{{item.logo}}"/></div>
				<div class="f2">
					 <div class="title">{{item.name}}</div>
					 <div class="score" ng-show="Item.params.showpingfen == 1"><img src="/static/img/star{{item.commentscore}}.png"/>{{item.comment_score}}分</div>
					 <div class="sales" ng-show="Item.params.showsales == 1"><span>销量：</span>{{item.sales}}</div>
					 <div class="address" ng-show="Item.params.showjianjie == 1">{{item.content}}</div>
					 <div class="address flex"><div class="flex1"><span ng-show="Item.params.showaddress == 1">{{item.address}}</span></div><div ng-show="Item.params.showdistance == 1" style="color:<?php echo t('color1'); ?>">200m</div></div>
				</div>
			</div>
			<div class="buspro" ng-show="Item.params.showproduct == 1">
				<div class="item" ng-repeat="item2 in item.prolist" ng-init="index = $index" style="{{'width:23%;margin-right:'+(index%4!=3?'2%':0)}}">
					<div class="product-pic">
						<img class="image" src="{{item2.pic}}"/>
					</div>
					<div class="product-info">
						<div class="p1">{{item2.name}}</div>
						<div class="p2">
							<div class="p2-1">
								<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item2.sell_price}}</span>
								<!-- <span class="t2" ng-if="item2.market_price*1 > item2.sell_price*1">￥{{item2.market_price}}</span> -->
							</div>
						</div>
						<!-- <div class="p3" ng-if="item.sales>0">已售{{item2.sales}}件</div> -->
						<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>"><span class="iconfont icon_gouwuche"></span></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-shortvideo.html"><div class="dsn-mod dsn-shortvideolist" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个短视频都没有...</div>
	<div class="shortvideolist">
		<div class="dsn-shortvideolist-item" ng-repeat="item in Item.data" ng-init="idx = $index" style="margin-right:{{idx%2==0?'2%':'0'}};">
			<img class="ff" src="{{item.coverimg}}"/>
			<div class="f2">
				<div class="t1" ng-if="Item.params.showlogo==1"><img class="touxiang" src="{{item.logo}}"/></div>
				<div class="t2" ng-if="Item.params.showviewnum==1"><img class="tubiao" src="/static/img/shortvideo_playnum.png"/>{{item.view_num}}</div>
				<div class="t3" ng-if="Item.params.showzannum==1"><img class="tubiao" src="/static/img/shortvideo_likenum.png"/>{{item.zan_num}}</div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-liveroom.html"><div class="dsn-mod dsn-livelist" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个直播间都没有...</div>
	<div class="livelist">
		<div class="livebox" ng-repeat="item in Item.data" ng-init="idx = $index" style="margin-right:{{idx%2==0?'2%':'0'}};">
			<div class="bgpic">
				<img src="{{item.coverImg}}"/>
			</div>
			<div class="f1" ng-if="item.status=='0'"><div class="t1">预告</div><div class="t2">{{item.showtime}}</div></div>
			<div class="f2" ng-if="item.status=='1'"><img src="/static/img/liveing.gif" class="t1"/><div class="t2">直播中</div></div>
			<div class="f3" ng-if="item.status=='2'"><div class="t1">已结束</div></div>
			<div class="f10">
				 <div class="title">{{item.name}}</div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-hotspot.html"><div class="dsn-mod dsn-hotspot" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'height':Item.params.height,'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0'}">
	<div>
		<img ng-src="{{Item.params.imgurl}}"/>
		<div ng-repeat="d in Item.data" ng-style="{'width':d.width+'px','height':d.height+'px','left':d.left+'px','top':d.top+'px'}" class="hotarea">{{d.hrefname}}</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-userinfo.html"><div class="dsn-mod" ng-class="{'dsn-mod-select':Item.id == focus}">
	<div ng-show="Item.params.style==1" style="background: linear-gradient(180deg, <?php echo t('color1'); ?> 0%, rgba(<?php echo t('color1rgb'); ?>,0) 100%);">
		<div class="dp-userinfo" ng-style="{'background':'url('+Item.params.bgimg+') no-repeat', 'background-size':'100% auto','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<div class="banner">
				<div class="info">
					<div class="f1">
						<img src="/static/img/touxiang.png" background-size="cover" class="headimg"/>
						<div class="flex-y-center">
							<div class="nickname">昵称</div>
							<div class='user-level' ng-show="Item.params.levelshow==1">
								<img class="level-img" src="/static/img/level_1.png" />
								<div class='level-name'>普通<?php echo t('会员'); ?></div>
							</div>
							<!-- Start: 会员卡升级模块 (Style 1) -->
							<div class='user-card-upgrade' ng-show="Item.params.cardupgradeshow == 1" ng-click="handleLink(Item.params.cardupgrade_hrefurl, '会员卡升级')" style="cursor:pointer; margin-top: 5px; padding: 5px 10px; background-color: rgba(255,255,255,0.2); border-radius: 15px; display: inline-block;">
								<span style="font-size: 12px; color: #fff;">立即升级 &gt;</span>
							</div>
							<!-- End: 会员卡升级模块 (Style 1) -->
						</div>
					</div>
					<div class='usercard' ng-show="Item.params.cardshow==1">
						<img src="/static/img/ico-card.png" class="img"/>
						<span class="t1">会员卡</span>
					</div>
				</div>
				<div class="custom_field" ng-show="Item.params.moneyshow==1 || Item.params.scoreshow==1 || Item.params.couponshow==1 || Item.params.commissionshow==1 || Item.params.gongxianzhishow==1 || Item.params.xianjinquanshow==1 || Item.params.shouyichishow==1 || Item.params.peizishow==1 || Item.params.huangjifenshow==1 || Item.params.creditshow==1 || Item.params.workshow==1 || Item.params.inviteshow==1">
					<div class='item' ng-show="Item.params.moneyshow==1">
						<span class='t2'>0.00</span>
						<span class="t1"><?php echo t('余额'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.commissionshow==1">
						<span class='t2'>0</span>
						<span class="t1"><?php echo t('佣金'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.scoreshow==1">
						<span class='t2'>0</span>
						<span class="t1"><?php echo t('积分'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.gongxianzhishow==1">
						<span class='t2'>0</span>
						<span class="t1"><?php echo t('贡献值'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.xianjinquanshow==1">
						<span class='t2'>0</span>
						<span class="t1"><?php echo t('现金券'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.shouyichishow==1">
						<span class='t2'>0</span>
						<span class="t1"><?php echo t('收益池'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.peizishow==1">
						<span class='t2'>{{userinfo.peizi || 0}}</span>
						<span class="t1"><?php echo t('配资'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.huangjifenshow==1">
						<span class='t2'>{{userinfo.scorehuang || 0}}</span>
						<span class="t1"><?php echo t('黄积分'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.creditshow==1">
						<span class='t2'>{{userinfo.credit_score || 0}}</span>
						<span class="t1"><?php echo t('信用分'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.couponshow==1">
						<span class='t2'>{{userinfo.coupon_count || 0}}</span>
						<span class="t1"><?php echo t('优惠券'); ?></span>
					</div>
					<div class='item' ng-show="Item.params.workshow==1">
						<span class='t2'>{{userinfo.work_count || 0}}</span>
						<span class="t1">工作经历</span>
					</div>
					<div class='item' ng-show="Item.params.inviteshow==1">
						<span class='t2'>{{userinfo.team_count || 0}}</span>
						<span class="t1">团队人数</span>
					</div>
				</div>
			</div>
			<div class="userset" ng-show="Item.params.seticonshow!=='0'"><img src="/static/img/set.png" class="img"/></div>
		</div>
	</div>
	<div ng-show="Item.params.style==2" style="background: linear-gradient(45deg, <?php echo t('color1'); ?> 0%, rgba(<?php echo t('color1rgb'); ?>, 0.8) 100%);">
		<div class="dp-userinfo2" ng-style="{'background':'url('+Item.params.bgimg+') no-repeat', 'background-size':'100% auto','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<div class="info">
				<img src="/static/img/touxiang.png" background-size="cover" class="headimg"/>
				<div class="nickname">
					<div class="nick">昵称</div>
					<div class='user-level' ng-show="Item.params.levelshow==1">
						<img class="level-img" src="/static/imgsrc/level_1.png" />
						<div class='level-name'>普通<?php echo t('会员'); ?></div>
					</div>
					<!-- Start: 会员卡升级模块 (Style 2) -->
					<div class='user-card-upgrade' ng-show="Item.params.cardupgradeshow == 1" ng-click="handleLink(Item.params.cardupgrade_hrefurl, '会员卡升级')" style="cursor:pointer; margin-top: 5px; padding: 5px 10px; background-color: rgba(255,255,255,0.8); border-radius: 15px; display: inline-block;">
						<span style="font-size: 12px; color: #333333;">立即升级 &gt;</span>
					</div>
					<!-- End: 会员卡升级模块 (Style 2) -->
				</div>
			</div>

			<div class='usercard' ng-show="Item.params.cardshow==1">
				<img src="/static/img/ico-card2.png" class="img"/>
				<span class="t1">会员卡</span>
			</div>

			<div class="qhsf" ng-show="Item.params.toggleidentity==1">切换身份</div>

			<div class="custom_field" ng-show="Item.params.moneyshow==1 || Item.params.scoreshow==1 || Item.params.couponshow==1 || Item.params.commissionshow==1 || Item.params.gongxianzhishow==1 || Item.params.xianjinquanshow==1 || Item.params.shouyichishow==1 || Item.params.peizishow==1 || Item.params.huangjifenshow==1 || Item.params.creditshow==1 || Item.params.workshow==1 || Item.params.inviteshow==1">
				<div class='item' ng-show="Item.params.moneyshow==1">
					<span class='t2'>0.00</span>
					<span class="t1"><?php echo t('余额'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.commissionshow==1">
					<span class='t2'>0</span>
					<span class="t1"><?php echo t('佣金'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.scoreshow==1">
					<span class='t2'>0</span>
					<span class="t1"><?php echo t('积分'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.gongxianzhishow==1">
					<span class='t2'>0</span>
					<span class="t1"><?php echo t('贡献值'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.xianjinquanshow==1">
					<span class='t2'>0</span>
					<span class="t1"><?php echo t('现金券'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.shouyichishow==1">
					<span class='t2'>0</span>
					<span class="t1"><?php echo t('收益池'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.peizishow==1">
					<span class='t2'>{{userinfo.peizi || 0}}</span>
					<span class="t1"><?php echo t('配资'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.huangjifenshow==1">
					<span class='t2'>{{userinfo.scorehuang || 0}}</span>
					<span class="t1"><?php echo t('黄积分'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.creditshow==1">
					<span class='t2'>{{userinfo.credit_score || 0}}</span>
					<span class="t1"><?php echo t('信用分'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.couponshow==1">
					<span class='t2'>{{userinfo.coupon_count || 0}}</span>
					<span class="t1"><?php echo t('优惠券'); ?></span>
				</div>
				<div class='item' ng-show="Item.params.workshow==1">
					<span class='t2'>{{userinfo.work_count || 0}}</span>
					<span class="t1">工作经历</span>
				</div>
				<div class='item' ng-show="Item.params.inviteshow==1">
					<span class='t2'>{{userinfo.team_count || 0}}</span>
					<span class="t1">团队人数</span>
				</div>
			</div>
			<div class="userset" ng-show="Item.params.seticonshow!=='0'"><img src="/static/img/set.png" class="img"/></div>
		</div>
	</div>

	<!-- Style 3 -->
	<div ng-show="Item.params.style==3" style="background: linear-gradient(135deg, <?php echo t('color1'); ?> 0%, rgba(<?php echo t('color1rgb'); ?>, 0.6) 100%);">
		<div class="dp-userinfo3" ng-style="{'background':'url('+Item.params.bgimg+') no-repeat', 'background-size':'100% auto','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<div class="profile-main" style="display:flex; align-items:center; padding:20px; background:rgba(255,255,255,0.95); border-radius:12px; margin-bottom:15px;">
				<img src="/static/img/touxiang.png" style="width:70px; height:70px; border-radius:50%; margin-right:15px; border:3px solid #fff; box-shadow:0 2px 8px rgba(0,0,0,0.1);"/>
				<div style="flex:1;">
					<div style="display:flex; align-items:center; margin-bottom:8px;">
						<span style="font-size:18px; font-weight:600; color:#333; margin-right:10px;">昵称</span>
						<span style="font-size:14px; color:#666;">(ID: 12345)</span>
					</div>
					<div class='user-level' ng-show="Item.params.levelshow==1" style="display:inline-flex; align-items:center; background:#f8f9fa; padding:4px 12px; border-radius:15px; margin-bottom:8px;">
						<img class="level-img" src="/static/img/level_1.png" style="width:16px; height:16px; margin-right:6px;"/>
						<span style="font-size:12px; color:#666;">普通<?php echo t('会员'); ?></span>
					</div>
					<div style="font-size:12px; color:#999; margin-top:5px;">
						邀请码：<span style="color:#333; font-weight:500;">ABC123</span>
					</div>
					<!-- 2025-01-03 22:55:53,565-INFO-[show-userinfo][parent_show_style3_001] Style3上级信息显示 -->
					<div ng-show="Item.params.parent_show" style="font-size:12px; color:#666; margin-top:5px;">
						<span style="color:#999; margin-right:8px;">上级：</span>
						<span style="color:#333; font-weight:500;">示例上级昵称</span>
					</div>
				</div>
				<div class="userset" ng-show="Item.params.seticonshow!=='0'" style="position:absolute; top:15px; right:15px;">
					<img src="/static/img/set.png" style="width:24px; height:24px;"/>
				</div>
			</div>
			
			<div class="assets-grid" ng-show="Item.params.moneyshow==1 || Item.params.scoreshow==1 || Item.params.couponshow==1 || Item.params.commissionshow==1 || Item.params.gongxianzhishow==1 || Item.params.xianjinquanshow==1 || Item.params.shouyichishow==1 || Item.params.peizishow==1 || Item.params.huangjifenshow==1 || Item.params.creditshow==1 || Item.params.workshow==1 || Item.params.inviteshow==1" style="display:grid; grid-template-columns:repeat(3,1fr); gap:10px; padding:0 5px;">
				<div class='item' ng-show="Item.params.moneyshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0.00</div>
					<div style="font-size:12px; color:#999;"><?php echo t('余额'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.commissionshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('佣金'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.scoreshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('积分'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.gongxianzhishow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('贡献值'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.xianjinquanshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('现金券'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.shouyichishow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('收益池'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.peizishow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('配资'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.huangjifenshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('黄积分'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.creditshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('信用分'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.couponshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;"><?php echo t('优惠券'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.workshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;">工作经历</div>
				</div>
				<div class='item' ng-show="Item.params.inviteshow==1" style="background:#fff; padding:15px; border-radius:8px; text-align:center; box-shadow:0 1px 3px rgba(0,0,0,0.1);">
					<div style="font-size:18px; font-weight:600; color:#333; margin-bottom:5px;">0</div>
					<div style="font-size:12px; color:#999;">团队人数</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Style 4 -->
	<div ng-show="Item.params.style==4" style="background: linear-gradient(to bottom, rgba(<?php echo t('color1rgb'); ?>, 0.1) 0%, #f8f9fa 100%);">
		<div class="dp-userinfo4" ng-style="{'background':'url('+Item.params.bgimg+') no-repeat', 'background-size':'100% auto','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<div class="profile-card" style="background:#fff; border-radius:16px; padding:20px; margin-bottom:15px; box-shadow:0 4px 12px rgba(0,0,0,0.08); position:relative;">
				<div class="userset" ng-show="Item.params.seticonshow!=='0'" style="position:absolute; top:15px; right:15px;">
					<img src="/static/img/set.png" style="width:24px; height:24px;"/>
				</div>
				
				<div style="display:flex; align-items:center; margin-bottom:20px;">
					<img src="/static/img/touxiang.png" style="width:60px; height:60px; border-radius:50%; margin-right:15px; border:2px solid #f0f0f0;"/>
					<div style="flex:1;">
						<div style="font-size:20px; font-weight:700; color:#333; margin-bottom:8px;">昵称</div>
						<div style="display:flex; align-items:center; gap:10px;">
							<span style="font-size:13px; color:#666;">ID: 12345</span>
							<div class='user-level' ng-show="Item.params.levelshow==1" style="background:linear-gradient(45deg, #ffd700, #ffed4a); color:#8b5a00; padding:2px 8px; border-radius:10px; font-size:11px; font-weight:600;">
								普通<?php echo t('会员'); ?>
							</div>
						</div>
						<div style="font-size:13px; color:#666; margin-top:8px;">
							邀请码：<span style="color:<?php echo t('color1'); ?>; font-weight:600;">ABC123</span>
						</div>
						<!-- 2025-01-03 22:55:53,565-INFO-[show-userinfo][parent_show_style4_001] Style4上级信息显示 -->
						<div ng-show="Item.params.parent_show" style="font-size:12px; color:#666; margin-top:5px;">
							<span style="color:#999; margin-right:8px;">上级：</span>
							<span style="color:#333; font-weight:500;">示例上级昵称</span>
						</div>
					</div>
				</div>
				
				<div class="assets-row" ng-show="Item.params.moneyshow==1 || Item.params.scoreshow==1 || Item.params.couponshow==1 || Item.params.commissionshow==1 || Item.params.gongxianzhishow==1 || Item.params.xianjinquanshow==1 || Item.params.shouyichishow==1 || Item.params.peizishow==1 || Item.params.huangjifenshow==1 || Item.params.creditshow==1 || Item.params.workshow==1 || Item.params.inviteshow==1" style="display:flex; flex-wrap:wrap; gap:15px;">
					<div class='item' ng-show="Item.params.moneyshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0.00</div>
						<div style="font-size:11px; color:#999;"><?php echo t('余额'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.commissionshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('佣金'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.scoreshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('积分'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.gongxianzhishow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('贡献值'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.xianjinquanshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('现金券'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.shouyichishow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('收益池'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.peizishow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('配资'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.huangjifenshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('黄积分'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.creditshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('信用分'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.couponshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;"><?php echo t('优惠券'); ?></div>
					</div>
					<div class='item' ng-show="Item.params.workshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;">工作经历</div>
					</div>
					<div class='item' ng-show="Item.params.inviteshow==1" style="flex:1; min-width:80px; text-align:center; padding:10px; border:1px solid #eee; border-radius:8px;">
						<div style="font-size:16px; font-weight:600; color:<?php echo t('color1'); ?>; margin-bottom:4px;">0</div>
						<div style="font-size:11px; color:#999;">团队人数</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Style 5 -->
	<div ng-show="Item.params.style==5" style="background: linear-gradient(to bottom, <?php echo t('color1'); ?> 0%, rgba(<?php echo t('color1rgb'); ?>,0.05) 100%);">
		<div class="dp-userinfo5" ng-style="{'background':'url('+Item.params.bgimg+') no-repeat', 'background-size':'100% auto','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px ' + '50px ' + Item.params.padding_x+'px'}">
			<div class="top-actions" style="position:absolute; top:15px; right:15px; display:flex; gap:10px; z-index:10;">
				<div class="userset" ng-show="Item.params.seticonshow!=='0'">
					<img src="/static/img/set.png" style="width:24px; height:24px;"/>
				</div>
				<div class='usercard' ng-show="Item.params.cardshow==1" style="display:flex; flex-direction:column; align-items:center; padding:8px; background:rgba(255,255,255,0.9); border-radius:8px;">
					<img src="/static/img/ico-card2.png" style="width:20px; height:20px; margin-bottom:4px;"/>
					<span style="font-size:10px; color:#333;">会员卡</span>
				</div>
			</div>
			
			<div class="profile-main" style="display:flex; align-items:center; padding:0; margin-bottom:20px;">
				<img src="/static/img/touxiang.png" style="width:80px; height:80px; border-radius:50%; margin-right:20px; border:4px solid rgba(255,255,255,0.3); box-shadow:0 4px 16px rgba(0,0,0,0.1);"/>
				<div style="flex:1; color:#fff;">
					<div style="display:flex; align-items:center; margin-bottom:8px;">
						<span style="font-size:22px; font-weight:700; margin-right:10px;">昵称</span>
						<span style="font-size:14px; opacity:0.8;">（ID: 12345）</span>
					</div>
					<div class='user-level' ng-show="Item.params.levelshow==1" style="display:inline-flex; align-items:center; background:rgba(255,255,255,0.2); padding:4px 12px; border-radius:15px; margin-bottom:10px;">
						<img class="level-img" src="/static/img/level_1.png" style="width:16px; height:16px; margin-right:6px;"/>
						<span style="font-size:12px; color:#fff;">普通<?php echo t('会员'); ?></span>
					</div>
					<div style="font-size:13px; opacity:0.9;">
						邀请码：<span style="font-weight:600; color:#fff;">ABC123</span>
					</div>
					<!-- 2025-01-03 22:55:53,565-INFO-[show-userinfo][parent_show_style5_001] Style5上级信息显示 -->
					<div ng-show="Item.params.parent_show" style="font-size:12px; color:rgba(255,255,255,0.8); margin-top:5px;">
						<span style="opacity:0.7; margin-right:8px;">上级：</span>
						<span style="font-weight:500;">示例上级昵称</span>
					</div>
				</div>
			</div>
			
			<!-- Assets Grid for Style 5 -->
			<div class="assets-grid-style5" ng-show="Item.params.moneyshow==1 || Item.params.scoreshow==1 || Item.params.couponshow==1 || Item.params.commissionshow==1 || Item.params.gongxianzhishow==1 || Item.params.xianjinquanshow==1 || Item.params.shouyichishow==1 || Item.params.peizishow==1 || Item.params.huangjifenshow==1 || Item.params.creditshow==1 || Item.params.workshow==1 || Item.params.inviteshow==1" style="background:#fff; border-radius:12px; padding:20px; margin-top:15px; box-shadow:0 2px 10px rgba(0,0,0,0.1); display:grid; grid-template-columns:repeat(3,1fr); gap:15px;">
				<div class='item' ng-show="Item.params.moneyshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0.00</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('余额'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.commissionshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('佣金'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.scoreshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('积分'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.gongxianzhishow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('贡献值'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.xianjinquanshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('现金券'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.shouyichishow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('收益池'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.peizishow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('配资'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.huangjifenshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('黄积分'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.creditshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('信用分'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.couponshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;"><?php echo t('优惠券'); ?></div>
				</div>
				<div class='item' ng-show="Item.params.workshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;">工作经历</div>
				</div>
				<div class='item' ng-show="Item.params.inviteshow==1" style="text-align:center; padding:15px 10px;">
					<div style="font-size:20px; font-weight:700; color:<?php echo t('color1'); ?>; margin-bottom:6px;">0</div>
					<div style="font-size:12px; color:#666; font-weight:500;">团队人数</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Referral Block -->
	<div class="dp-userinfo-referral" 
		 ng-style="{
			'margin-top': Item.params.padding_y + 'px',
			'margin-right': Item.params.padding_x + 'px',
			'margin-bottom': Item.params.padding_y + 'px',
			'margin-left': Item.params.padding_x + 'px',
			'padding': '12px 15px', 
			'background': '#fff', 
			'border-radius': '8px',
			'box-shadow': '0 1px 3px rgba(0,0,0,0.05)'
		 }" 
		 ng-show="Item.params.referralshow==1">
		<div style="display:flex; justify-content:space-between; align-items:center; width:100%; padding: 12px 15px;">
			<div style="display:flex; align-items:center;">
				<span style="font-size:15px; color:#333; margin-right:15px; font-weight:500;">推荐人</span>
				<img ng-src="{{userinfo.referral_avatar || '/static/img/touxiang.png'}}" style="width:36px; height:36px; border-radius:50%; margin-right:8px;"/>
				<span style="font-size:14px; color:#555;">{{userinfo.referral_name || '示例推荐人'}}</span>
			</div>
			<div style="display:flex; align-items:center; font-size:13px; color:#888; cursor:pointer;" ng-click="viewReferralInfo()">
				<span>查看信息</span>
				<img src="/static/img/arrowright.png" style="width:12px; height:12px; margin-left:4px;"/>
			</div>
		</div>

		<!-- Internal Actions Container -->
		<div class="referral-internal-actions"
			 ng-show="Item.params.referral_team_show==1 || Item.params.referral_support_show==1"
			 ng-style="{
				'background-color': Item.params.referral_actions_bgcolor || '#f9f9f9',
				'border-radius': '0 0 ' + (Item.params.referral_actions_radius || 8) + 'px ' + (Item.params.referral_actions_radius || 8) + 'px', 
				'padding': '10px 0',
				'margin-top': '10px',
                'border-top': '1px solid #eee',
				'display': 'flex',
				'justify-content': 'space-around',
				'align-items': 'center'
			 }">

			<div class="action-item" ng-show="Item.params.referral_team_show==1" ng-click="showTeamInfoPopup(Item.id)"
				 style="display:flex; flex-direction:row; align-items:center; cursor:pointer; padding: 5px 10px; text-align: center; flex:1; justify-content: center;">
				<img ng-src="{{Item.params.referral_team_icon || '/static/img/dsn_team_icon.png'}}" style="width:20px; height:20px; margin-right:5px;"/>
				<span style="font-size:12px; color:#333;">{{Item.params.referral_team_text || '团队信息'}}</span>
			</div>

			<div class="action-item" ng-show="Item.params.referral_support_show==1" ng-click="handleLink(Item.params.referral_support_link_url, Item.params.referral_support_link_name)"
				 style="display:flex; flex-direction:row; align-items:center; cursor:pointer; padding: 5px 10px; text-align: center; flex:1; justify-content: center;">
				<img ng-src="{{Item.params.referral_support_icon || '/static/img/dsn_support_icon.png'}}" style="width:20px; height:20px; margin-right:5px;"/>
				<span style="font-size:12px; color:#333;">{{Item.params.referral_support_text || '联系官方'}}</span>
			</div>
		</div>
	</div>

	<div class="dp-userinfo-order" ng-style="{'margin':Item.params.padding_y+'px '+Item.params.padding_x+'px','margin-top':Item.params.style==2?'-50px':'0'}" ng-show="Item.params.ordershow==1">
		<div class="head">
			<span class="f1">我的订单</span>
			<div class="f2"><span>查看全部订单</span><img src="/static/img/arrowright.png" class="image"/></div>
		</div>
		<div class="content">
			 <div class="item">
					<span class="iconfont icondaifukuan" style="font-size:30px;color:<?php echo t('color1'); ?>;"></span>
					<span class="t3">待付款</span>
			 </div>
			 <div class="item">
					<span class="iconfont icondaifahuo" style="font-size:30px;color:<?php echo t('color1'); ?>;"></span>
					<span class="t3">待发货</span>
			 </div>
			 <div class="item">
					<span class="iconfont icondaishouhuo" style="font-size:30px;color:<?php echo t('color1'); ?>;"></span>
					<span class="t3">待收货</span>
			 </div>
			 <div class="item">
					<span class="iconfont iconyiwancheng" style="font-size:30px;color:<?php echo t('color1'); ?>;"></span>
					<span class="t3">退款/售后</span>
			 </div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-supervipcard.html"><div class="dsn-mod" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px'}">
	<div ng-show="Item.params.bgimgshow==1" style="background: linear-gradient(180deg, <?php echo t('color1'); ?> 0%, rgba(<?php echo t('color1rgb'); ?>,0) 100%);">
		<div class="dp-userinfo" ng-style="{'background':'url('+Item.params.bgimg+') no-repeat', 'background-size':'100% auto','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<div class="banner" style="padding: unset;">

				<div class="custom_field" ng-style="{'border-radius':'8px','background':Item.params.bgcolor}">
					<div class='item' ng-repeat="list in Item.data" ng-show="list.show == 1">
						<span class='t2' ng-style="{'font-size':Item.params.fontsize+'px','color':Item.params.color,'font-weight': 'unset'}">{{list.text}}</span>
						<span class="t1" ng-style="{'font-size':Item.params.numfontsize+'px','color':Item.params.numcolor}">
						    {{list.value}}</span>
					</div>

					<div class='item' ng-show="Item.params.codeshow==1">
						<span class='t2'><img src="{{Item.params.usercodeimg}}" class="img" ng-style="{'width':Item.params.usercodeimgsize+'px','height':Item.params.usercodeimgsize+'px'}"></span>
						<span class="t1" ng-style="{'font-size':Item.params.fontsize+'px','color':Item.params.color,'font-weight': 'unset'}">会员码</span>
					</div>
				</div>

			</div>
		</div>
	</div>

	<div ng-show="Item.params.bgimgshow==0">
		<div class="dp-userinfo" ng-style="{'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
			<div class="banner" style="margin-top: 0px;padding: unset;">
				<div class="custom_field" ng-style="{'border-radius':'8px','background':Item.params.bgcolor}">
					<div class='item' ng-repeat="list in Item.data" ng-show="list.show == 1">
						<span class='t2' ng-style="{'font-size':Item.params.fontsize+'px','color':Item.params.color,'font-weight': 'unset'}">{{list.text}}</span>
						<span class="t1" ng-style="{'font-size':Item.params.numfontsize+'px','color':Item.params.numcolor}">{{totalmoney}}</span>
					</div>

					<div class='item' ng-show="Item.params.codeshow==1">
						<span class='t2'><img src="{{Item.params.usercodeimg}}" class="img" ng-style="{'width':Item.params.usercodeimgsize+'px','height':Item.params.usercodeimgsize+'px'}"></span>
						<span class="t1" ng-style="{'font-size':Item.params.fontsize+'px','color':Item.params.color,'font-weight': 'unset'}">会员码</span>
					</div>
				</div>
			</div>
		</div>
	</div>

</div></script>
	<!--预约服务-->
	<script type="text/ng-template" id="show-yuyue.html"><div class="dsn-mod dp-yuyue" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
	<!--123排-->
	<div class="dp-yuyue-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
		<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
							<span class="t1" style="color:<?php echo t('color1'); ?>">{{item.sell_price}}<span style="font-size:12px;padding-left:2px">元/{{item.danwei}}</span></span>
					</div>
				</div>
				<div class="p3" ng-if="Item.params.showsales != '0'">已售{{item.sales}}</div>
			</div>
		</div>
	</div>
	<div class="dp-yuyue-itemlist" ng-show="Item.params.style=='list'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}" mode="widthFix"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2" ng-if="Item.params.showprice != '0'">
					<span class="t1" style="color:<?php echo t('color1'); ?>">{{item.sell_price}}<span style="font-size:12px;padding-left:2px">元/{{item.danwei}}</span></span>
				</div>
				<div class="p3" ng-if="Item.params.showsales != '0'">已售{{item.sales}}</div>
			</div>
		</div>
	</div>
	<div class="dp-yuyue-itemline" ng-show="Item.params.style=='line'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>">{{item.sell_price}}<span style="font-size:12px;padding-left:2px">元/{{item.danwei}}</span></span>
					</div>
				</div>
				<div class="p3" ng-if="Item.params.showsales != '0'">已售{{item.sales}}</div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-tuangou.html"><div class="dsn-mod dp-tuangou" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
	<!--123排-->
	<div class="dp-tuangou-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
		<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">低至￥</span>{{item.min_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已售{{item.sales}}件</div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1"><span class="iconfont icon_gouwuche"></span></div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2"><img src="{{Item.params.cartimg}}" class="img"/></div>
			</div>
		</div>
	</div>
	<div class="dp-tuangou-itemlist" ng-show="Item.params.style=='list'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}" mode="widthFix"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2" ng-if="Item.params.showprice != '0'">
					<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px">低至￥</span>{{item.min_price}}</span>
					<span class="t2" ng-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
				</div>
				<div class="p3">
					<div class="p3-1" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已售{{item.sales}}件</span></div>
				</div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1"><span class="iconfont icon_gouwuche"></span></div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2"><img src="{{Item.params.cartimg}}" class="img"/></span></div>
			</div>
		</div>
	</div>
	<div class="dp-tuangou-itemline" ng-show="Item.params.style=='line'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">低至￥</span>{{item.min_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已售{{item.sales}}件</div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1"><span class="iconfont icon_gouwuche"></span></div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2"><img src="{{Item.params.cartimg}}" class="img"/></div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-luckycollage.html"><div class="dsn-mod dp-luckycollage" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个商品都没有...</div>
	<!--123排-->
	<div class="dp-collage-item" ng-show="Item.params.style=='2'">
		<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
				<div class="desc">
					<span>{{item.teamnum}}人拼团 {{item.gua_num}}人得商品</span>
					<span>{{item.teamnum-item.gua_num}}人各得{{item.money}}元参与奖</span>
				</div>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3">
					<div class="p3-1" style="background:rgba(<?php echo t('color1rgb'); ?>,0.12);color:<?php echo t('color1'); ?>">{{item.teamnum}}人团</div>
					<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已拼成{{item.sales}}件</span></div>
				</div>
			</div>
		</div>
	</div>
	<div class="dp-collage-itemlist lucky_itemlist" ng-show="Item.params.style=='list'">
		<div class="item" ng-repeat="item in Item.data" style="display:inline-block">
			<div class="flex">
				<div class="product-pic">
					<img class="image" src="{{item.pic}}" mode="widthFix"/>
					<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
				</div>

				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">	<span class="team_text">{{item.teamnum}}人团</span>{{item.name}}</div>
					<div class="box_2 flex" style="justify-content: space-between;">
						<div class="p2" ng-if="Item.params.showprice != '0'">
							<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px">￥</span>{{item.sell_price}}</span>
							<span class="t2" v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
						</div>
						<div style="overflow:hidden;float:right;line-height:30px">已拼成{{item.sales}}件</div>
					</div>
					<div class="p3">
						<div class="p3-2" ng-if="Item.params.showsales=='1' && item.sales>0"> </div>
					</div>
				</div>
			</div>
			<div class="desc"><span>{{item.teamnum}}人拼团 {{item.gua_num}}人得商品</span><span v-if="item.gua_num>0">{{item.teamnum-item.gua_num}}人各得{{item.money}}元参与奖</span></div>
			
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-kecheng.html"><div class="dsn-mod dp-product" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<style>
	.dp-product-recommend {
		padding: 10px; /* Or use Item.params.padding_x, Item.params.padding_y if that's how other components are padded */
	}
	.dp-product-recommend-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
		padding: 0;
		border-radius: 0;
	}
	.dp-product-recommend-title .left-text {
		font-size: 16px;
		font-weight: bold;
	}
	.dp-product-recommend-title .right-refresh {
		font-size: 13px;
		color: #888; /* Placeholder color */
		cursor: pointer;
	}
	.dp-product-recommend-title .right-refresh .fa-refresh {
		margin-left: 4px;
	}
	.item-first {
		position: relative;
		margin-bottom: 10px;
	}
	.item-first .product-pic img.image {
		width: 100%;
		display: block;
		border-radius: 6px;
	}
	.item-first .product-name-mask {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 40px;
		background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
		border-bottom-left-radius: 6px;
		border-bottom-right-radius: 6px;
	}
	.item-first .product-name {
		position: absolute;
		bottom: 8px;
		left: 10px;
		right: 10px;
		color: #fff; /* White text for overlay */
		font-size: 14px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	.item-list .item {
		display: flex;
		align-items: center;
		margin-bottom: 8px;
	}
	.item-list .item:last-child {
		margin-bottom: 0;
	}
	.item-list .item .product-pic-small img.image {
		width: 120px; 
		height: 75px; 
		object-fit: cover;
		border-radius: 4px;
		margin-right: 10px;
	}
	.item-list .item .product-info-small .p1 {
		font-size: 13px;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		line-height: 1.4;
	}
	/* Boutique Style */
	.dp-product-boutique {
		padding: 10px; /* Consistent padding */
	}
	.dp-product-boutique-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
		padding: 0;
		border-radius: 0;
	}
	.dp-product-boutique-title .left-text {
		font-size: 16px;
		font-weight: bold;
	}
	.dp-product-boutique-title .right-refresh {
		font-size: 13px;
		color: #888; /* Placeholder color */
		cursor: pointer;
	}
	.dp-product-boutique-title .right-refresh .fa-refresh {
		margin-left: 4px;
	}
	.dp-product-boutique-grid {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -5px; /* Gutter compensation */
	}
	.dp-product-boutique-grid .item {
		width: calc(50% - 10px); /* 2 columns with gutter */
		margin: 0 5px 10px 5px;
		background-color: #fff; /* Assuming a white background for items */
		border-radius: 6px;
		overflow: hidden; /* To contain image border radius */
		box-shadow: 0 2px 4px rgba(0,0,0,0.05); /* Optional: subtle shadow */
	}
	.dp-product-boutique-grid .item .product-pic img.image {
		width: 100%;
		display: block;
		aspect-ratio: 16/9; /* Or your desired aspect ratio */
		object-fit: cover;
	}
	.dp-product-boutique-grid .item .product-info {
		padding: 8px;
	}
	.dp-product-boutique-grid .item .product-info .p1 {
		font-size: 13px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-bottom: 4px; /* Space before price/other info if any */
	}
	</style>
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个课程都没有...</div>
	<!--123排-->
	<div class="dp-product-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
		<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px" ng-if="item.price>0">￥</span>{{item.price==0?'免费':item.price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3">共{{item.count}}节<span ng-if="Item.params.showsales=='1'"> | {{item.join_num}}人已加入学习</span></div>
			</div>
		</div>
	</div>
	<div class="dp-product-itemlist" ng-show="Item.params.style=='list'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}" mode="widthFix"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2" ng-if="Item.params.showprice != '0'">
					<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px" ng-if="item.price>0">￥</span>{{item.price==0?'免费':item.price}}</span>
					<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.price*1">￥{{item.market_price}}</span>
				</div>
				<div class="p3">
					<div class="p3-1">共{{item.count}}节<span ng-if="Item.params.showsales=='1'"> | {{item.join_num}}人已加入学习</span></div>
				</div>
			</div>
		</div>
	</div>
	<div class="dp-product-itemline" ng-show="Item.params.style=='line'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px" ng-if="item.price>0">￥</span>{{item.price==0?'免费':item.price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3">共{{item.count}}节<span ng-if="Item.params.showsales=='1'"> | {{item.join_num}}人已加入学习</span></div>
			</div>
		</div>
	</div>
	<div class="dp-product-recommend" ng-show="Item.params.style=='recommend'">
		<div class="dp-product-recommend-title" ng-style="{
			'background': Item.params.titleBgType === 'solid' && Item.params.titleBgSolid ? Item.params.titleBgSolid : (Item.params.titleBgType === 'gradient' && Item.params.titleBgGradientStart && Item.params.titleBgGradientEnd ? 'linear-gradient(' + (Item.params.titleBgGradientDirection || 'to right') + ', ' + Item.params.titleBgGradientStart + ', ' + Item.params.titleBgGradientEnd + ')' : null),
			'padding': (Item.params.titleBgType === 'solid' && Item.params.titleBgSolid) || (Item.params.titleBgType === 'gradient' && Item.params.titleBgGradientStart && Item.params.titleBgGradientEnd) ? '8px 10px' : null,
			'border-radius': (Item.params.titleBgType === 'solid' && Item.params.titleBgSolid) || (Item.params.titleBgType === 'gradient' && Item.params.titleBgGradientStart && Item.params.titleBgGradientEnd) ? '6px 6px 0 0' : null
		}">
			<div class="left-text" style="display: flex; align-items: center; flex-grow: 1;">
				<span ng-if="Item.params.showTitleLeftBorder == '1'"
					  class="title-left-pipe"
					  ng-style="{
						  'background-color': Item.params.titleLeftBorderColor || '#FF8C00',
						  'width': '4px',
						  'height': '1em',
						  'margin-right': '8px',
						  'display': 'inline-block'
					  }"></span>
				<span class="main-title-content" ng-style="{'color': Item.params.titleTextColor || null}">
					{{ Item.params.titleText || '热门课程' }}
					<span ng-if="Item.params.showTitleBottomBorder == '1'"
						  class="title-bottom-underline"
						  ng-style="{
							  'background-color': Item.params.titleBottomBorderColor || '#FF8C00',
							  'height': '3px',
							  'width': '25px',
							  'display': 'block',
							  'margin-top': '4px'
						  }"></span>
				</span>
			</div>
			<div class="right-refresh" ng-click="refreshRecommend(Item.id)" ng-style="{'color': Item.params.refreshTextColor || null}">
				{{Item.params.refreshText || '换一换'}} <i class="fa fa-refresh"></i>
			</div>
		</div>
		<div class="item-first" ng-if="Item.data && Item.data.length > 0">
			<div class="product-pic">
				<img class="image" ng-src="{{Item.data[0].pic}}"/>
				<img class="saleimg" ng-src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg && Item.params.saleimg!=''"/>
			</div>
			<div class="product-name-mask"></div>
			<div class="product-name" ng-if="Item.params.showname == 1">{{Item.data[0].name}}</div>
		</div>
		<div class="item-list">
			<div class="item" ng-repeat="item in Item.data track by $index" ng-if="$index > 0">
				<div class="product-pic-small">
					<img class="image" ng-src="{{item.pic}}"/>
				</div>
				<div class="product-info-small">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				</div>
			</div>
		</div>
	</div>
	<div class="dp-product-boutique" ng-show="Item.params.style=='boutique'">
		<div class="dp-product-boutique-title" ng-style="{
			'background': Item.params.titleBgType === 'solid' && Item.params.titleBgSolid ? Item.params.titleBgSolid : (Item.params.titleBgType === 'gradient' && Item.params.titleBgGradientStart && Item.params.titleBgGradientEnd ? 'linear-gradient(' + (Item.params.titleBgGradientDirection || 'to right') + ', ' + Item.params.titleBgGradientStart + ', ' + Item.params.titleBgGradientEnd + ')' : null),
			'padding': (Item.params.titleBgType === 'solid' && Item.params.titleBgSolid) || (Item.params.titleBgType === 'gradient' && Item.params.titleBgGradientStart && Item.params.titleBgGradientEnd) ? '8px 10px' : null,
			'border-radius': (Item.params.titleBgType === 'solid' && Item.params.titleBgSolid) || (Item.params.titleBgType === 'gradient' && Item.params.titleBgGradientStart && Item.params.titleBgGradientEnd) ? '6px 6px 0 0' : null
		}">
			<div class="left-text" style="display: flex; align-items: center; flex-grow: 1;">
				<span ng-if="Item.params.showTitleLeftBorder == '1'"
					  class="title-left-pipe"
					  ng-style="{
						  'background-color': Item.params.titleLeftBorderColor || '#FF8C00',
						  'width': '4px',
						  'height': '1em',
						  'margin-right': '8px',
						  'display': 'inline-block'
					  }"></span>
				<span class="main-title-content" ng-style="{'color': Item.params.titleTextColor || null}">
					{{ Item.params.titleText || '精品课程' }}
					<span ng-if="Item.params.showTitleBottomBorder == '1'"
						  class="title-bottom-underline"
						  ng-style="{
							  'background-color': Item.params.titleBottomBorderColor || '#FF8C00',
							  'height': '3px',
							  'width': '25px',
							  'display': 'block',
							  'margin-top': '4px'
						  }"></span>
				</span>
			</div>
			<div class="right-refresh" ng-click="refreshBoutique(Item.id)" ng-style="{'color': Item.params.refreshTextColor || null}">
				{{Item.params.refreshText || '换一换'}} <i class="fa fa-refresh"></i>
			</div>
		</div>
		<div class="dp-product-boutique-grid">
			<div class="item" ng-repeat="item in Item.data track by $index">
				<div class="product-pic">
					<img class="image" ng-src="{{item.pic}}"/>
					<img class="saleimg" ng-src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg && Item.params.saleimg!=''"/>
				</div>
				<div class="product-info">
					<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
					<!-- Price and other details can be added here if needed, similar to other styles -->
				</div>
			</div>
		</div>
	</div>
</div></script>

	<script type="text/ng-template" id="show-zhaopin.html"><div class="dsn-mod dsn-zhaopin" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">暂无招聘信息...</div>
	<div class="buslist">
		<div ng-repeat="item in Item.data" ng-init="idx = $index" class="busbox" style="background: #fff; margin-bottom: 15px; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
			<div class="businfo" style="display: flex; align-items: center;">
				<div style="margin-right: 15px;">
					<img src="{{item.logo}}" style="width: 50px; height: 50px; object-fit: cover;"/>
				</div>
				<div>
					<div class="title" style="font-size: 16px; margin-bottom: 5px;">{{item.name}}</div>
					<div style="color: #666; font-size: 14px;">职位数：{{item.job_count}}</div>
				</div>
			</div>
		</div>
	</div>
</div>
</script>
	<script type="text/ng-template" id="edit-zhaopin.html">
<div class="dsn-panel-editor-title">招聘企业设置</div>
<div class="dsn-panel-editor-content">
	<div class="layui-form-item">
		<div class="layui-form-label">与上方距离</div>
		<div class="slider" min="0" max="40" ng-model="Edit.params.margin_y"></div>
		<span class="slider-span"><input ng-model="Edit.params.margin_y"/> px</span>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">背景颜色</div>
		<div class="layui-input-inline">
			<input class="layui-input" value="{{Edit.params.bgcolor}}" ng-model="Edit.params.bgcolor"/>
		</div>
		<span class="colorpicker" ng-model="Edit.params.bgcolor"></span>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">企业规模</div>
		<div class="layui-input-inline">
			<input class="radio" type="radio" name="{{Edit.id}}_showscale" title="显示" value="1" ng-model="Edit.params.showscale" />
			<input class="radio" type="radio" name="{{Edit.id}}_showscale" title="不显示" value="0" ng-model="Edit.params.showscale"/>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">职位数量</div>
		<div class="layui-input-inline">
			<input class="radio" type="radio" name="{{Edit.id}}_showjobcount" title="显示" value="1" ng-model="Edit.params.showjobcount" />
			<input class="radio" type="radio" name="{{Edit.id}}_showjobcount" title="不显示" value="0" ng-model="Edit.params.showjobcount"/>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">企业简介</div>
		<div class="layui-input-inline">
			<input class="radio" type="radio" name="{{Edit.id}}_showjianjie" title="显示" value="1" ng-model="Edit.params.showjianjie" />
			<input class="radio" type="radio" name="{{Edit.id}}_showjianjie" title="不显示" value="0" ng-model="Edit.params.showjianjie"/>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">企业地址</div>
		<div class="layui-input-inline">
			<input class="radio" type="radio" name="{{Edit.id}}_showaddress" title="显示" value="1" ng-model="Edit.params.showaddress" />
			<input class="radio" type="radio" name="{{Edit.id}}_showaddress" title="不显示" value="0" ng-model="Edit.params.showaddress"/>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">距离</div>
		<div class="layui-input-inline">
			<input class="radio" type="radio" name="{{Edit.id}}_showdistance" title="显示" value="1" ng-model="Edit.params.showdistance" />
			<input class="radio" type="radio" name="{{Edit.id}}_showdistance" title="不显示" value="0" ng-model="Edit.params.showdistance"/>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">热门职位</div>
		<div class="layui-input-inline">
			<input class="radio" type="radio" name="{{Edit.id}}_showjobs" title="显示" value="1" ng-model="Edit.params.showjobs" />
			<input class="radio" type="radio" name="{{Edit.id}}_showjobs" title="不显示" value="0" ng-model="Edit.params.showjobs"/>
		</div>
	</div>

	<div class="layui-form-item">
		<div class="layui-form-label">企业来源</div>
		<div class="layui-input-inline" style="width:500px">
			<input class="radio" type="radio" name="{{Edit.id}}_companyfrom" value="0" ng-model="Edit.params.companyfrom" title="手动选择"/> 
			<input class="radio" type="radio" name="{{Edit.id}}_companyfrom" value="1" ng-model="Edit.params.companyfrom" title="企业分类" ng-change="getZhaopin(Edit.id)"/> 
		</div>
	</div>
	<div ng-show="Edit.params.companyfrom == '0'">
		<div ng-repeat="good in Edit.data" class="dsn-panel-editor-relative">
			<div class="dsn-panel-editor-del" style="right:30px;font-size:12px" title="上移" ng-click="upItemChild(Edit.id,good.id)"><i class="fa fa-arrow-up"></i></div>
			<div class="dsn-panel-editor-del" title="移除" ng-click="delcompany(Edit.id,good.id)">×</div>
			<div class="dsn-panel-editor-line flex">
				<div class="dsn-panel-editor-goodimg flex0" ng-click="addcompany('replace',Edit.id, good.id)">
					<img ng-src="{{good.logo}}" width="100%"/>
					<div class="dsn-panel-editor-goodimg-t2">重新选择企业</div>
				</div>
				<div class="dsn-panel-editor-line-right">
					<div class="layui-form-item">
						<div class="layui-form-label">企业名称</div>
						<div class="layui-form-mid">{{good.name}}</div>
					</div>
					<div class="layui-form-item">
						<div class="layui-form-label">企业地址</div>
						<div class="layui-form-mid">{{good.address}}</div>
					</div>
				</div>
			</div>
		</div>
		<div class="layui-btn layui-btn-primary" style="width:500px;margin-top:10px" ng-click="addcompany('',Edit.id,'')"><i class="fa fa-plus-circle"></i> 添加一个企业</div>
	</div>
	<div ng-show="Edit.params.companyfrom == '1'">
		<div class="layui-form-item">
			<div class="layui-form-label">企业分类</div>
			<div class="layui-input-inline">
				<select class="layui-input" style="width:200px;-webkit-appearance:menulist" lay-ignore ng-model="Edit.params.category" ng-change="getZhaopin(Edit.id)">
					<option value="">全部分类</option>
					<?php foreach($zhaopin_clist as $cv): ?>
						<option value="<?php echo $cv['id']; ?>"><?php echo $cv['name']; ?></option>
						<?php foreach($cv['child'] as $v): ?>
						<option value="<?php echo $v['id']; ?>">&nbsp;&nbsp;&nbsp;<?php echo $v['name']; ?></option>
						<?php endforeach; ?>
					<?php endforeach; ?>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-form-label">排序方式</div>
			<div class="layui-input-inline" style="width:500px">
				<input class="radio" type="radio" name="{{Edit.id}}_sortby" value="sort" ng-model="Edit.params.sortby" ng-change="getZhaopin(Edit.id)" title="默认"/> 
				<input class="radio" type="radio" name="{{Edit.id}}_sortby" value="createtimedesc" ng-model="Edit.params.sortby" ng-change="getZhaopin(Edit.id)" title="创建时间↓"/></i>
				<input class="radio" type="radio" name="{{Edit.id}}_sortby" value="createtime" ng-model="Edit.params.sortby" ng-change="getZhaopin(Edit.id)" title="创建时间↑"/></i>
				<input class="radio" type="radio" name="{{Edit.id}}_sortby" value="jobcount" ng-model="Edit.params.sortby" ng-change="getZhaopin(Edit.id)" title="职位数量"/> 
				<input class="radio" type="radio" name="{{Edit.id}}_sortby" value="juli" ng-model="Edit.params.sortby" ng-change="getZhaopin(Edit.id)" title="距离"/> 
				<input class="radio" type="radio" name="{{Edit.id}}_sortby" value="rand" ng-model="Edit.params.sortby" ng-change="getZhaopin(Edit.id)" title="随机"/> 
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-form-label">显示数量</div>
			<div class="layui-input-inline">
				<input class="layui-input" ng-model="Edit.params.shownum" ng-blur="getZhaopin(Edit.id)"/>
			</div>
		</div>
		<div class="layui-form-item" ng-show="Edit.params.sortby == 'juli'">
			<div class="layui-form-label">显示范围</div>
			<div class="slider" min="1" max="100" ng-model="Edit.params.distance"></div>
			<span class="slider-span"><input ng-model="Edit.params.distance"/> km</span>
		</div>
	</div>
	
	<hr>
	<div class="layui-form-item">
		<div class="layui-form-label">左右边距</div>
		<div class="slider" min="0" max="40" ng-model="Edit.params.margin_x"></div>
		<span class="slider-span"><input ng-model="Edit.params.margin_x"/> px</span>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">左右内边距</div>
		<div class="slider" min="0" max="40" ng-model="Edit.params.padding_x"></div>
		<span class="slider-span"><input ng-model="Edit.params.padding_x"/> px</span>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">上下内边距</div>
		<div class="slider" min="0" max="40" ng-model="Edit.params.padding_y"></div>
		<span class="slider-span"><input ng-model="Edit.params.padding_y"/> px</span>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">显示平台</div>
		<div class="layui-input-inline" style="width:500px">
			<input class="checkbox" type="checkbox" lay-skin="primary" title="所有平台" name="{{Edit.id}}_platform['all']" ng-model="Edit.params.platform['all']"/>
			<?php foreach($platform as $pl): ?>
			<input class="checkbox" type="checkbox" lay-skin="primary" title="<?php echo getplatformname($pl); ?>" name="{{Edit.id}}_platform['<?php echo $pl; ?>']" ng-model="Edit.params.platform['<?php echo $pl; ?>']"/>
			<?php endforeach; ?>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-form-label">显示范围</div>
		<div class="layui-input-inline" style="width:500px">
			<input class="checkbox" type="checkbox" lay-skin="primary" title="所有人" name="{{Edit.id}}_quanxian['all']" ng-model="Edit.params.quanxian['all']"/>
			<?php foreach($memberlevelList as $lv): ?>
			<input class="checkbox" type="checkbox" lay-skin="primary" title="<?php echo $lv['name']; ?>" name="{{Edit.id}}_quanxian[<?php echo $lv['id']; ?>]" ng-model="Edit.params.quanxian[<?php echo $lv['id']; ?>]"/>
			<?php endforeach; ?>
		</div>
	</div>
	<div class="layui-form-item" ng-show="!Edit.params.quanxian['all']">
		<div class="layui-form-label">显示用户</div>
		<div class="layui-input-inline">
			<input class="layui-input" ng-model="Edit.params.showmids"/>
		</div>
		<div class="layui-form-mid layui-word-aux">填写用户ID，多个用英文标点逗号(,)分割</div>
	</div>
</div>
</script>
	<script type="text/ng-template" id="show-qiuzhi.html"><?php if(getcustom('zhaopin')): ?>
<div class="dsn-mod dp-article" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
    <div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;" ng-show="Item.data == ''">一条求职都没有...</div>
    <!--单排-->
    <div ng-show="Item.params.style=='1'" ng-repeat="item in Item.data" class="article-itemlist">
        <div class="article-pic">
            <img class="image" src="{{item.thumb}}" style="{{(item.secret_type==2 || item.secret_type==3)?('filter: blur('+item.mohu+'px);-webkit-filter: blur('+item.mohu+'px);-moz-filter: blur('+item.mohu+'px)'):''}}"/>
        </div>
        <div class="article-info">
            <div class="p1">{{item.title}}</div>
            <div class="p2">
                <span style="overflow:hidden" class="flex1">{{item.name}}
                    <span ng-if="item.sex=='1'">/男</span>
                    <span ng-if="item.sex=='2'">/女</span>
                    /{{item.age}}岁</span>
                <span style="overflow:hidden">{{item.salary}}</span>
            </div>
            <div class="p3" style="color: #666666">
                {{item.cnames}}
            </div>
<!--            <div class="p3" style="color: #666666">-->
<!--                {{item.area}}-->
<!--            </div>-->
        </div>
    </div>
    <!--双排-->
    <div ng-show="Item.params.style=='2'" ng-repeat="item in Item.data" class="article-item2" ng-init="index = $index" style="margin-right:{{index%2==0?'2%':'0'}}">
        <div class="article-pic">
            <img class="image" src="{{item.thumb}}"/>
        </div>
        <div class="article-info">
            <div class="p1">{{item.name}}</div>
            <div class="p2">
<!--                <span style="overflow:hidden" class="flex1" >-->
<!--                    <span ng-if="item.sex=='1'">/男</span>-->
<!--                    <span ng-if="item.sex=='2'">/女</span>-->
<!--                    /{{item.age}}岁</span>-->
<!--                </span>-->
                <span style="overflow:hidden" >{{item.cnames}}</span>
            </div>
        </div>
    </div>
</div>
<?php endif; ?></script>
	<script type="text/ng-template" id="edit-qiuzhi.html"><?php if(getcustom('zhaopin')): ?>
<div class="dsn-panel-editor-title">求职设置</div>
<div class="dsn-panel-editor-content">
    <div class="layui-form-item">
        <div class="layui-form-label">与上方距离</div>
        <div class="slider" min="0" max="40" ng-model="Edit.params.margin_y"></div>
        <span class="slider-span"><input ng-model="Edit.params.margin_y"/> px</span>
    </div>
    <div class="layui-form-item">
        <div class="layui-form-label">样式选择</div>
        <div class="layui-input-inline" style="width:500px">
            <input class="radio" type="radio" name="{{Edit.id}}_style" value="1" ng-model="Edit.params.style" ng-change="changeImg(Edit.id, Edit.params.style)" title="单排"/>
            <input class="radio" type="radio" name="{{Edit.id}}_style" value="2" ng-model="Edit.params.style" ng-change="changeImg(Edit.id, Edit.params.style)" title="双排"/>
        </div>
    </div>

    <div class="layui-form-item">
        <div class="layui-form-label">背景颜色</div>
        <div class="layui-input-inline">
            <input class="layui-input" value="{{Edit.params.bgcolor}}" ng-model="Edit.params.bgcolor"/>
        </div>
        <span class="colorpicker" ng-model="Edit.params.bgcolor"></span>
    </div>
    <div class="layui-form-item">
        <div class="layui-form-label">排序方式</div>
        <div class="layui-input-inline" style="width:500px">
            <input class="radio" type="radio" name="{{Edit.id}}_sortby" value="sort" ng-model="Edit.params.sortby" ng-change="getzhaopin(Edit.id)" title="按排序"/>
            <input class="radio" type="radio" name="{{Edit.id}}_sortby" value="createtimedesc" ng-model="Edit.params.sortby" ng-change="getzhaopin(Edit.id)" title="发布时间↓"/></i>
            <input class="radio" type="radio" name="{{Edit.id}}_sortby" value="createtime" ng-model="Edit.params.sortby" ng-change="getzhaopin(Edit.id)" title="发布时间↑"/></i>
            <input class="radio" type="radio" name="{{Edit.id}}_sortby" value="rand" ng-model="Edit.params.sortby" ng-change="getzhaopin(Edit.id)" title="随机"/>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-form-label">显示数量</div>
        <div class="layui-input-inline">
            <input class="layui-input" ng-model="Edit.params.shownum" ng-blur="getzhaopin(Edit.id)"/>
        </div>
    </div>
    <hr>
    <div class="layui-form-item">
        <div class="layui-form-label">左右边距</div>
        <div class="slider" min="0" max="40" ng-model="Edit.params.margin_x"></div>
        <span class="slider-span"><input ng-model="Edit.params.margin_x"/> px</span>
    </div>
    <div class="layui-form-item">
        <div class="layui-form-label">左右内边距</div>
        <div class="slider" min="0" max="40" ng-model="Edit.params.padding_x"></div>
        <span class="slider-span"><input ng-model="Edit.params.padding_x"/> px</span>
    </div>
    <div class="layui-form-item">
        <div class="layui-form-label">上下内边距</div>
        <div class="slider" min="0" max="40" ng-model="Edit.params.padding_y"></div>
        <span class="slider-span"><input ng-model="Edit.params.padding_y"/> px</span>
    </div>
    <div class="layui-form-item">
        <div class="layui-form-label">显示平台</div>
        <div class="layui-input-inline" style="width:500px">
            <input class="checkbox" type="checkbox" lay-skin="primary" title="所有平台" name="{{Edit.id}}_platform['all']" ng-model="Edit.params.platform['all']"/>
            <?php foreach($platform as $pl): ?>
            <input class="checkbox" type="checkbox" lay-skin="primary" title="<?php echo getplatformname($pl); ?>" name="{{Edit.id}}_platform['<?php echo $pl; ?>']" ng-model="Edit.params.platform['<?php echo $pl; ?>']"/>
            <?php endforeach; ?>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-form-label">显示范围</div>
        <div class="layui-input-inline" style="width:500px">
            <input class="checkbox" type="checkbox" lay-skin="primary" title="所有人" name="{{Edit.id}}_quanxian['all']" ng-model="Edit.params.quanxian['all']"/>
            <?php foreach($memberlevelList as $lv): ?>
            <input class="checkbox" type="checkbox" lay-skin="primary" title="<?php echo $lv['name']; ?>" name="{{Edit.id}}_quanxian[<?php echo $lv['id']; ?>]" ng-model="Edit.params.quanxian[<?php echo $lv['id']; ?>]"/>
            <?php endforeach; ?>
        </div>
    </div>
    <div class="layui-form-item" ng-show="!Edit.params.quanxian['all']">
        <div class="layui-form-label">显示用户</div>
        <div class="layui-input-inline">
            <input class="layui-input" ng-model="Edit.params.showmids"/>
        </div>
        <div class="layui-form-mid layui-word-aux">填写用户ID，多个用英文标点逗号(,)分割
        </div>
    </div>
</div>
<?php endif; ?></script>

	<script type="text/ng-template" id="show-wxad.html"><div class="dsn-mod dsn-wxad" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<img src="/static/imgsrc/wxad.png" style="width:100%;height:auto">
</div></script>
	<script type="text/ng-template" id="show-restaurant_product.html"><div class="dsn-mod dp-product" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个菜品都没有...</div>
	<!--123排-->
	<div class="dp-product-item" ng-show="Item.params.style=='1' || Item.params.style=='2' || Item.params.style=='3'">
		<div class="item" ng-repeat="item in Item.data" ng-init="index = $index" style="{{Item.params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (Item.params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')}}">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{Item.params.saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已售{{item.sales}}件</div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1"><span class="iconfont icon_gouwuche"></span></div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2"><img src="{{Item.params.cartimg}}" class="img"/></div>
			</div>
		</div>
	</div>
	<div class="dp-product-itemlist" ng-show="Item.params.style=='list'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}" mode="widthFix"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2" ng-if="Item.params.showprice != '0'">
					<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px;padding-right:1px">￥</span>{{item.sell_price}}</span>
					<span class="t2" ng-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
				</div>
				<div class="p3">
					<div class="p3-1" ng-if="Item.params.showsales=='1' && item.sales>0"><span style="overflow:hidden">已售{{item.sales}}件</span></div>
				</div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1"><span class="iconfont icon_gouwuche"></span></div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2"><img src="{{Item.params.cartimg}}" class="img"/></span></div>
			</div>
		</div>
	</div>
	<div class="dp-product-itemline" ng-show="Item.params.style=='line'">
		<div class="item" ng-repeat="item in Item.data">
			<div class="product-pic">
				<img class="image" src="{{item.pic}}"/>
				<img class="saleimg" src="{{saleimg}}" ng-if="Item.params.saleimg!=''"/>
			</div>
			<div class="product-info">
				<div class="p1" ng-if="Item.params.showname == 1">{{item.name}}</div>
				<div class="p2">
					<div class="p2-1" ng-if="Item.params.showprice != '0'">
						<span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item.sell_price}}</span>
						<span class="t2" ng-if="Item.params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</span>
					</div>
				</div>
				<div class="p3" ng-if="Item.params.showsales=='1' && item.sales>0">已售{{item.sales}}件</div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==1"><span class="iconfont icon_gouwuche"></span></div>
				<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>" ng-if="Item.params.showcart==2"><img src="{{Item.params.cartimg}}" class="img"/></div>
			</div>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-jidian.html"><div class="dsn-mod dsn-jidian-warp" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'border-radius':Item.params.borderradius+'px','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div class="dp-jidian"
		ng-style="{}">
		<div class="dp-jidian-f1"></div>
		<div class="dp-jidian-f2">
			<p class="text-big">集满5单返券</p>
			<p>再下<span>4单</span>得<span>4元无门槛</span>优惠券</p>
		</div>
		<div class="dp-jidian-f3">
			<span class="circle circleCheck"><i></i></span>
			<span class="circle"></span>
			<span class="circle"></span>
			<span class="circle"></span>
			<span class="circle"></span>
		</div>
	</div>
</div></script>
	<script type="text/ng-template" id="show-tab.html"><div class="dsn-mod dsn-tab" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'color':Item.params.color,'border-radius':Item.params.borderradius+'px','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0'}">
	<div class="dsn-tab-box" ng-style="{'background-color':Item.params.bgcolor,'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
		<div ng-repeat="d in Item.data" class="dsn-tab-item" ng-style="{'color':Item.params.selectindex==$index?Item.params.color2:Item.params.color1,'font-size':Item.params.fontsize1+'px','width':Item.params.max_width}">{{d.name}}<div class="dsn-tab-item-after" ng-style="{background:Item.params.arrowcolor}" ng-show="Item.params.arrowshow==1 && Item.params.selectindex==$index"></div></div>
	</div>
	<div style="height:200px;line-height:200px; text-align: center; color: #999; font-size: 16px;" ng-style="{'background-color':Item.params.bgcolor2,'padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">选项卡内容区域</div>
</div></script>
	<script type="text/ng-template" id="show-form-log.html"><div class="dsn-mod dsn-form-log-warp" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'border-radius':Item.params.borderradius+'px','margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div class="dp-form-log" ng-style="{}">
		<div class="flex head">
			<div class="left">{{Item.params.title}}</div>
			<div class="right">全部xx条<img src="/static/img/arrowright.png" style="width:16px;height:16px"/></div>
		</div>
		<div class="text">
			<div class="title">昵称xx 填写了{{Item.data.name}}</div>
			<div>2020-11-11 10:00</div>
		</div>
	</div>
</div></script>

	<script type="text/ng-template" id="show-venues.html"><div class="dsn-mod dsn-business" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个场馆都没有...</div>
	<div class="buslist">
		<div ng-repeat="item in Item.data" ng-init="idx = $index" class="busbox">
			<div class="businfo">
				<div class="f1"><img src="{{item.logo}}"/></div>
				<div class="f2">
					 <div class="title">{{item.title}}</div>
					 <!-- <div class="score" ng-show="Item.params.showpingfen == 1"><img src="/static/img/star{{item.commentscore}}.png"/>{{item.comment_score}}分</div> -->
					 <!-- <div class="sales" ng-show="Item.params.showsales == 1"><span>销量：</span>{{item.sales}}</div> -->
					 <div class="address flex">
					 	<div class="flex1">
					 		<span ng-show="Item.params.showaddress == 1">{{item.location}}</span>
					 	</div>
					 	<div ng-show="Item.params.showdistance == 1" style="color:<?php echo t('color1'); ?>">200m</div>
					 </div>
					 <div class="address flex" ng-show="Item.params.showdservice == 1">
					 	<div class="" ng-repeat="item2 in item.service">
					 		<span style="color: #31C88E;margin-right: 4px;border: 1px solid #31C88E;padding: 0 2px;">{{item2.name}}</span>
					 	</div>
					 </div>
					 <div class="address" ng-show="Item.params.showjianjie == 1">{{item.introduce}}</div>
				</div>
			</div>
			<div class="buspro" ng-show="Item.params.showproduct == 1">
				<div class="item" ng-repeat="item2 in item.prolist" ng-init="index = $index" style="{{'width:23%;margin-right:'+(index%4!=3?'2%':0)}}">
					<div class="product-pic">
						<img class="image" src="{{item2.pic}}"/>
					</div>
					<div class="product-info">
						<div class="p1">{{item2.name}}</div>
						<div class="p2">
							<div class="p2-1">
								<!-- <span class="t1" style="color:<?php echo t('color1'); ?>"><span style="font-size:12px">￥</span>{{item2.sell_price}}</span> -->
								<!-- <span class="t2" ng-if="item2.market_price*1 > item2.sell_price*1">￥{{item2.market_price}}</span> -->
							</div>
						</div>
						<!-- <div class="p3" ng-if="item.sales>0">已售{{item2.sales}}件</div> -->
						<div class="p4" style="background:rgba(<?php echo t('color1rgb'); ?>,0.1);color:<?php echo t('color1'); ?>"><span class="iconfont icon_gouwuche"></span></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div></script>

	<script type="text/ng-template" id="show-electricity_form.html"><div class="dsn-mod dsn-form" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'color':Item.params.color,'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px','font-size':Item.params.fontsize+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;" ng-if="Item.data == ''">没有表单信息...</div>
	
  <div class="{{Item.params.style==1?'item':'item2'}}" ng-repeat="(idx,item) in Item.data.content" style="border-color:{{Item.params.linecolor}}" ng-if="Item.params.isquery!=1 || item.query == 1">
    <span class='label'>{{item.val1}}<span ng-if="item.val3==1 && Item.params.showmuststar && Item.params.isquery!=1" style="color:red"> *</span></span>
		<input ng-if="item.key=='input'" type="text" name="form{{idx}}" class="input" placeholder="{{item.val2}}" style="background-color:{{Item.params.inputbgcolor}};border-color:{{Item.params.inputbordercolor}}"/>
		<textarea ng-if="item.key=='textarea'" name="form{{idx}}" class='textarea' placeholder="{{item.val2}}" style="background-color:{{Item.params.inputbgcolor}};border-color:{{Item.params.inputbordercolor}}"></textarea>
		<div class="radio1" ng-if="item.key=='radio'" style="flex-wrap:wrap">
			<div ng-repeat="item1 in item.val2" class="radio2">
				<div class="myradio"></div>{{item1}}<span style="padding-left:10px"></span>
			</div>
		</div>
		<div class="checkbox1" ng-if="item.key=='checkbox'" style="flex-wrap:wrap">
			<div ng-repeat="item1 in item.val2" class='checkbox2'>
				<div class="mycheckbox"></div>{{item1}}<span style="padding-left:10px"></span>
			</div>
		</div>
		<div ng-if="item.key=='switch'" class="layui-unselect layui-form-switch" lay-skin="_switch"><em></em><i></i></div>
	
		<picker ng-if="item.key=='selector'" mode="selector" name="form{{idx}}" value="" class='xyy-pic' range="{{item.val2}}" bindchange="bindPickerChange" data-idx="{{idx}}">
				<div class="picker" ng-if="item.val2[formdata[idx]]">当前选择: {{item.val2[formdata[idx]]}}</div>
				<div wx:else>请选择</div>
		</picker>
		<picker ng-if="item.key=='time'" mode="time" name="form{{idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" class="xyy-pic" range="{{item.val2}}" bindchange="bindPickerChange" data-idx="{{idx}}">
				<div class="picker" ng-if="formdata[idx]">当前选择: {{formdata[idx]}}</div>
				<div wx:else>请选择</div>
		</picker>
		<picker ng-if="item.key=='date'" mode="date" name="form{{idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" class="xyy-pic" range="{{item.val2}}" bindchange="bindPickerChange" data-idx="{{idx}}">
				<div class="picker" ng-if="formdata[idx]">当前选择: {{formdata[idx]}}</div>
				<div wx:else>请选择</div>
		</picker>
		<picker ng-if="item.key=='region'" mode="region" name="form{{idx}}" value="" class="xyy-pic" bindchange="bindPickerChange" data-idx="{{idx}}">
				<div class="picker" ng-if="formdata[idx]">当前选择: {{formdata[idx]}}</div>
				<div wx:else>请选择省市区</div>
		</picker>
		<div ng-if="item.key=='upload'" style="margin-left:10px">
				<img src="/static/imgsrc/upload.png" style="width:60px;height:60px"/>
		</div>
		<div ng-if="item.key=='upload_file'" style="margin-left:10px">
				<img src="/static/imgsrc/upload.png" style="width:60px;height:60px"/>
		</div>
	</div>
	<div class="item" ng-if="Item.data.payset==1 && Item.params.isquery!=1">
			<span class='label'>支付金额</span>
			<input type="text" class="input" name="money" value='{{Item.data.price}}' ng-if="Item.data.priceedit==1"/>
			<span class="" ng-if="Item.data.priceedit==0">{{Item.data.price}}</span>
			<span style="padding-left:5px">元</span>
	</div>
	<div ng-if="Item.data != ''" class="form-btn" style="background-color:{{Item.params.btnbgcolor}};border:1px solid {{Item.params.btnbordercolor}};font-size:{{Item.params.btnfontsize}}px;color:{{Item.params.btncolor}};width:{{Item.params.btnwidth}}px;height:{{Item.params.btnheight}}px;line-height:{{Item.params.btnheight}}px;border-radius:{{Item.params.btnradius}}px">{{Item.params.btntext}}</div>
</div></script>
	<script type="text/ng-template" id="show-daihuoyiuan.html"><div class="dsn-mod dp-daihuoyiuan" ng-class="{'dsn-mod-select':Item.id == focus}" ng-style="{'background-color':Item.params.bgcolor,'margin':Item.params.margin_y+'px '+Item.params.margin_x+'px 0','padding':Item.params.padding_y+'px '+Item.params.padding_x+'px'}">
	<div style="line-height: 170px; text-align: center; color: #999; font-size: 16px;width:100%" ng-show="Item.data == ''">一个都没有...</div>

		<div class="item-list" ng-repeat="item in Item.data" ng-init="idx = $index">
			<div class="item-header">
				<img class="item-image" src="{{item.pic}}" ng-show="Item.params.showlogo=='1'"></img>
				<div class="item-author">{{item.author}}</div>
			</div> 
			<div class="item-name">{{item.name}}</div>
			<div class="item-price">
				<span class="currency-symbol">￥</span>
				<span class="price-value">{{item.priceRange}}</span>
			</div>
			<div class="item-pics">
				<img class="item-pic" ng-repeat="items in item.pic2" src="{{items}}" ></img>
			</div>
			<div class="item-footer">
				<div class="viewers">
					<img class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></img>
					<img class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></img>
					<img class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></img>
					<div class="view-count" ng-show="Item.params.showviewnum=='1'">{{item.readcount}}人看过</div>
				</div>
				<div class="share">
					<img class="share-icon" src="../../static/img/share.png"></img>
					<div class="share-text">点击分享</div>
				</div>
			</div>
		</div>

</div></script>
	
</div>
<script src="/static/admin/layui/layui.all.js"></script>
<script type="text/javascript" src="/static/admin/js/angular.min.js"></script>

<script type="text/javascript" src="/static/admin/js/hhSwipe.js"></script>
<script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=ABLBZ-4BIKU-GFTVB-BK7IK-OLQ35-QCBFF"></script>
<script type="text/javascript">
function initswipe(jobj){
	var bullets = jobj.next().get(0).getElementsByTagName('a');
	var banner = Swipe(jobj.get(0), {
		auto: 4000,
		continuous: true,
		disableScroll:false,
		callback: function(pos) {
			var i = bullets.length;
			while (i--) {
				$(bullets[i]).css("opacity",0.4);
			}
			$(bullets[pos]).css("opacity",0.6);
		}
	})
}
var app = angular.module('myApp', []);
app.filter('price', function() {
	return function(text){
		return parseFloat(text).toString().split('.')[0];
	}
});
app.filter('price_dot', function() {
	return function(text){
		var pricearr = parseFloat(text).toString().split('.');
		if(pricearr[1]) return '.'+pricearr[1];
		else return '';
	}
});
app.controller('MainCtrl', ['$scope', function($scope){
	$scope.cols = [0,1,2,3];
	$scope.size = $(document.body).width()/4;
	$scope.pages = <?php echo $pageinfo; ?>;
	$scope.system = {name:'<?php echo $sysset['name']; ?>',logo:'<?php echo $sysset['logo']; ?>'};
	$scope.Items = <?php echo $pagedata; ?>;   //初始数据
	$scope.show = '1';
	$scope.hasCube = function(Item){
		var has = false;
		var row=0,col = 0;
		for(var i=row;i<4;i++){
			for(var j=col;j<4;j++){
				if (Item.params.layout[i][j] && !Item.params.layout[i][j].isempty) {
					has = true;
					break;
				}
			}
		}
		return has;
	}
	$scope.$on('ngRepeatFinished',function(ngRepeatFinishedEvent){
		$('.dsn-banner .swipe').each(function(){
			initswipe($(this));
		});
		$('.dsn-cube table  tr').each(function(){
			if( $(this).children().length<=0){
				$(this).html('<td></td>');
			}
		});
		angular.forEach($scope.Items, function(m, index1) {
			if(m.temp == 'map'){
				var center = new qq.maps.LatLng(m.params.latitude,m.params.longitude);
				var map = new qq.maps.Map(document.getElementById("showmp_"+m.id), {
					center: center,      // 地图的中心地理坐标。
					zoom:15              // 地图的中心地理坐标。
				});
				var marker = new qq.maps.Marker({ position:center, map: map});
			}
		})
	});
}]);
app.directive('stringHtml' , function(){
	return function(scope , el , attr){
		if(attr.stringHtml){
			scope.$watch(attr.stringHtml , function(html){
				el.html(html || '');
			});
		}
	};
});
app.directive("onFinishRenderFilters",function($timeout){
	return{
		restrict: 'A',
		link: function(scope,element,attr){
			if(scope.$last === true){
				$timeout(function(){
					scope.$emit('ngRepeatFinished');
				});
			}
		}
	};
});
</script>

</html>
