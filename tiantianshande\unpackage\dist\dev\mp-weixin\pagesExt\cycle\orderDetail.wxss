
page {
	background: #F6F6F6;
}


.banner.data-v-fe824c5c {
		position: relative;
		width: 100%;
}
.banner_img.data-v-fe824c5c {
		width: 100%;
		display: block;
}
.banner_icon.data-v-fe824c5c {
		position: absolute;
		right: 70rpx;
		top: 30rpx;
		height: 124rpx;
		width: 124rpx;
}
.banner_data.data-v-fe824c5c {
		position: absolute;
		top: 0;
		width: 100%;
		box-sizing: border-box;
		padding: 60rpx 60rpx 0 60rpx;
}
.banner_title.data-v-fe824c5c {
		font-size: 40rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
}
.banner_text.data-v-fe824c5c {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(255, 255, 255, 0.6);
		margin-top: 28rpx;
}
.address.data-v-fe824c5c{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}
.address .img.data-v-fe824c5c{width:40rpx}
.address image.data-v-fe824c5c{width:40rpx; height:40rpx;}
.address .info.data-v-fe824c5c{flex:1;display:flex;flex-direction:column;}
.address .info .t1.data-v-fe824c5c{font-size:28rpx;font-weight:bold;color:#333}
.address .info .t2.data-v-fe824c5c{font-size:24rpx;color:#999}
.body.data-v-fe824c5c {
		position: relative;
		width: 690rpx;
		box-sizing: border-box;
		padding: 30rpx 30rpx 0 30rpx;
		margin: -235rpx auto 0 auto;
		background: #fff;
		border-radius: 10rpx;
}
.body_title.data-v-fe824c5c {
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #111111;
}
.body_module.data-v-fe824c5c {
		padding: 45rpx 0 20rpx 0;
		display: flex;
}
.body_data.data-v-fe824c5c {
		flex: 1;
}
.body_img.data-v-fe824c5c {
		width: 172rpx;
		height: 172rpx;
		border-radius: 10rpx;
		margin-right: 30rpx;
		flex-shrink: 0;
}
.body_name.data-v-fe824c5c {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #323232;
}
.body_text.data-v-fe824c5c {
		font-size: 24rpx;
		font-weight: 500;
		color: #999999;
		margin-top: 15rpx;
}
.body_price.data-v-fe824c5c {
		font-size: 32rpx;
		font-family: Arial;
		font-weight: bold;
		color: #FD4A46;
		margin-top: 30rpx;
}
.body_tag.data-v-fe824c5c {
		font-size: 20rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FD4A46;
}
.body_num.data-v-fe824c5c {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #222222;
}
.body_list.data-v-fe824c5c {
		position: relative;
		width: 630rpx;
		height: 88rpx;
		margin: 0 auto;
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #222222;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #f7f7f7;
}
.body_list.data-v-fe824c5c:last-child {
		border-bottom: 0;
}
.body_time.data-v-fe824c5c {
		display: flex;
		align-items: center;
}
.body_time ._img.data-v-fe824c5c {
		height: 35rpx;
		width: 35rpx;
		margin-left: 15rpx;
}
.body_color.data-v-fe824c5c {
		color: #FF5347;
}
.opt.data-v-fe824c5c {
		position: relative;
		width: 100%;
		height: 105rpx;
		margin-top: 30rpx;
}
.opt_module.data-v-fe824c5c {
		position: fixed;
		height: 105rpx;
		width: 100%;
		background: #fff;
		bottom: 0;
		padding: 0 40rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		box-shadow: 0px 0px 18px 0px rgba(132, 132, 132, 0.3200);
}
.opt_btn.data-v-fe824c5c {
		width: 160rpx;
		height: 60rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #333;
		background: #fff;
		text-align: center;
		line-height: 60rpx;
		margin-left: 10rpx;
		border: 1px solid #cdcdcd
}
.orderinfo.data-v-fe824c5c{width:100%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 0;background: #FFF;}
.orderinfo .item.data-v-fe824c5c{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}
.orderinfo .item.data-v-fe824c5c:last-child{ border-bottom: 0;}
.orderinfo .item .t1.data-v-fe824c5c{width:200rpx;flex-shrink:0;font-size: 26rpx}
.orderinfo .item .t2.data-v-fe824c5c{flex:1;text-align:right}
.orderinfo .item .t3.data-v-fe824c5c{ margin-top: 3rpx;}
.orderinfo .item .red.data-v-fe824c5c{color:red}

	/* 	.opt_btn:last-child {
		color: #fff;
		background: #FD4A46;
	} */

