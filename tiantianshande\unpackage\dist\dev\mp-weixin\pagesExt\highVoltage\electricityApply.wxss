.header {
  position: relative;
  width: 100%;
  height: 360rpx;
  background-color: #3d8b6c;
  padding-top: 22rpx;
}
.header .top {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.header .top .logo {
  width: 206rpx;
  height: 206rpx;
}
.header .top .title-box {
  line-height: 64rpx;
  color: #ffffff;
  font-size: 28rpx;
}
.header .top .title {
  font-size: 48rpx;
}
.apply {
  background-color: #ffffff;
  padding: 16rpx 0;
  margin-top: 118rpx;
}
.btn {
  width: 438rpx;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 8rpx;
  background-color: #3d8b6c;
  color: white;
  font-size: 28rpx;
  text-align: center;
  font-family: <PERSON><PERSON>;
  margin: auto;
}
.list {
  min-height: 520rpx;
  overflow-y: scroll;
  margin-top: 44rpx;
}
.list .title {
  margin-left: 46rpx;
  font-weight: bold;
  color: #101010;
  font-size: 32rpx;
}
.list .box {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 30rpx 40rpx;
  color: #101010;
}
.list .box .box-title {
  line-height: 44rpx;
  font-size: 32rpx;
  text-align: left;
  font-weight: bold;
}
.list .box .time {
  display: flex;
  align-items: center;
  margin: 10rpx 0;
  color: #8e8e93;
  font-size: 24rpx;
}
.list .box .time .icon {
  margin-right: 10rpx;
  width: 22.92rpx;
  height: 23.98rpx;
}
.list .box .remark {
  font-size: 24rpx;
}
.container {
  margin-top: 108rpx;
  background-color: #ffffff;
  padding: 20rpx 18rpx 28rpx;
}
.container .title {
  color: #101010;
  font-size: 28rpx;
}
.form {
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}
.form .form-item {
  background-color: #ffffff;
  padding: 18rpx 30rpx 30rpx;
}
.form .form-item .title {
  color: #aeaeb2;
  margin-bottom: 26rpx;
}
.form .form-item .value {
  color: #000000;
  font-weight: bold;
}
.form .box {
  margin-bottom: 18rpx;
}
.form .input {
  height: 80rpx;
  font-size: 28rpx;
  border: 2rpx solid #bbbbbb;
  padding: 0 20rpx;
}
.form .textarea {
  padding: 20rpx;
  width: 100%;
  height: 200rpx;
}
.content {
  padding-bottom: 20rpx;
}
.back {
  padding: 34rpx 34rpx;
}
.back .btn {
  border-radius: 100rpx;
}
.back-i {
  margin-top: 30rpx;
  padding: 34rpx 34rpx;
  width: 516rpx;
  height: 100rpx;
  line-height: 40rpx;
  border-radius: 100rpx;
  background-color: #3d8b6c;
  color: white;
  box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(0, 0, 0, 0.1);
}

