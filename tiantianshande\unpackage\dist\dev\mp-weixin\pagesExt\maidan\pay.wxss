
page {
		background: #f0f0f0;
}
.container {
		position: fixed;
		height: 100%;
		width: 100%;
		overflow: hidden;
		z-index: 5;
}
.header {
		position: relative;
		padding: 30rpx;
}
.header_text {
		font-size: 24rpx;
		color: #666;
}
.header_name {
		font-size: 36rpx;
		color: #333;
		font-weight: bold;
}
.header_icon {
		position: relative;
		height: 85rpx;
		width: 85rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
		background: #37b053;
}
.header_shop {
		font-size: 28rpx;
		color: #333;
		margin-top: 10rpx;
}
.page {
		position: relative;
		padding: 20rpx 50rpx 20rpx 50rpx;
		border-radius: 30rpx 30rpx 0 0;
		background: #fff;
		box-sizing: border-box;
		width: 100%;
		height: calc(100% - 185rpx);
}
.page_title {
		font-size: 24rpx;
		color: #333;
}
.page_module {
		position: relative;
		height: 125rpx;
		border-bottom: 1px solid #f0f0f0;
}
.page_notice {
		color: #999;
		font-size: 32rpx;
		font-weight: normal;
}
.page_tag {
		font-size: 58rpx;
		color: #333;
		font-weight: bold;
}
.page_price {
		margin-left: 20rpx;
		font-size: 54rpx;
		color: #333;
		font-weight: bold;
}
.page_cursor {
		width: 4rpx;
		height: 70rpx;
		background: #1AAD19;
		border-radius: 6rpx;
		-webkit-animation: twinkling 1.5s infinite;
		        animation: twinkling 1.5s infinite;
}
@-webkit-keyframes twinkling {
0% {
			opacity: 0;
}
90% {
			opacity: .8;
}
100% {
			opacity: 1;
}
}
@keyframes twinkling {
0% {
			opacity: 0;
}
90% {
			opacity: .8;
}
100% {
			opacity: 1;
}
}
.info-box {
		position: relative;
		background: #fff;
}
.info-item {
		display: flex;
		align-items: center;
		border-bottom: 1px #f3f3f3 solid;
}
.info-item:last-child {
		border: none
}
.info-item .t1 {
		width: 200rpx;
		height: 120rpx;
		line-height: 120rpx;
		color: #000;
}
.info-item .t2 {
		height: 120rpx;
		line-height: 120rpx;
		color: #000;
		text-align: right;
		flex: 1;
		font-size: 28rpx
}
.info-item .t2 input {
		height: 80rpx;
		line-height: 80rpx;
		border: 1px solid #f5f5f5;
		padding: 0 5px;
		width: 240rpx;
		font-size: 30rpx;
		margin-right: 10rpx
}
.dkdiv {
		margin-top: 20rpx
}
.dkdiv-item {
		width: 100%;
		padding: 30rpx 0;
		background: #fff;
		border-bottom: 1px #ededed solid;
}
.dkdiv-item:last-child {
		border: none;
}
.dkdiv-item .f1 {}
.dkdiv-item .f2 {
		text-align: right;
		flex: 1
}
.dkdiv-item .f3 {
		width: 30rpx;
		height: 30rpx;
}
.fpay-btn {
		width: 90%;
		margin: 0 5%;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 40rpx;
		float: left;
		border-radius: 10rpx;
		color: #fff;
		background: #1aac19;
		border: none;
		font-size: 30rpx;
}
.fpay-btn2 {
		width: 90%;
		margin: 0 5%;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 20rpx;
		float: left;
		border-radius: 10rpx;
		color: #fff;
		background: #e2cc05;
		border: none;
		font-size: 30rpx;
}
.mendian {
		width: 90%;
		line-height: 60rpx;
		border-radius: 10rpx;
		padding: 30rpx 5%;
		height: 800rpx;
		overflow-y: scroll;
		border: none;
		border-radius: 5px;
		-webkit-animation-duration: .5s;
		animation-duration: .5s;
}
.mendian label {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #f5f5f5;
		padding: 20rpx 0;
		color: #333
}
.mendian input {
		margin-right: 10rpx
}
.submit {
		text-align: center
}
.mendian button {
		padding: 20rpx 60rpx;
		border-radius: 40rpx;
		border: 0;
		margin-top: 20rpx;
		color: #fff;
		background: #31C88E
}
.i-as {
		position: fixed;
		width: 100%;
		box-sizing: border-box;
		left: 0;
		right: 0;
		bottom: 0;
		background: #f7f7f8;
		-webkit-transform: translate3d(0, 100%, 0);
		        transform: translate3d(0, 100%, 0);
		-webkit-transform-origin: center;
		        transform-origin: center;
		transition: all .2s ease-in-out;
		z-index: 900;
		visibility: hidden
}
.i-as-show {
		-webkit-transform: translate3d(0, 0, 0);
		        transform: translate3d(0, 0, 0);
		visibility: visible
}
.i-as-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, .7);
		z-index: 900;
		transition: all .2s ease-in-out;
		opacity: 0;
		visibility: hidden
}
.i-as-mask-show {
		opacity: 1;
		visibility: visible
}
.i-as-header {
		background: #fff;
		text-align: center;
		position: relative;
		font-size: 30rpx;
		color: #555;
		height: 80rpx;
		line-height: 80rpx
}
.i-as-header::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 200%;
		height: 200%;
		-webkit-transform: scale(.5);
		        transform: scale(.5);
		-webkit-transform-origin: 0 0;
		        transform-origin: 0 0;
		pointer-events: none;
		box-sizing: border-box;
		border: 0 solid #e9eaec;
		border-bottom-width: 1px
}
.i-as-cancel {
		margin-top: 20rpx
}
.i-as-cancel button {
		border: 0
}
.i-as-cancel button::after {
		border: 0;
}
.i-as-content {
		height: 700rpx;
		width: 710rpx;
		margin: 20rpx;
}
.op {
		width: 96%;
		margin: 20rpx 2%;
		display: flex;
		align-items: center;
		margin-top: 40rpx
}
.op .btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		background: #07C160;
		width: 90%;
		margin: 0 10rpx;
		border-radius: 10rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center
}
.op .btn .img {
		width: 48rpx;
		height: 48rpx;
		margin-right: 20rpx
}
.keyboard_page {
		position: fixed;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
		z-index: 999;
}
.keyboard_none {
		position: absolute;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
}
.keyboard_key {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 0;
		z-index: 10;
		background: #f7f7f7;
		z-index: 9999999999;
		transition: height 0.3s;
		padding: 20rpx 0 0 0;
}
.hind_box {
		height: 515rpx;
}
.key-box {
		display: flex;
		padding-left: 16rpx;
		padding-bottom: 16rpx;
		padding-bottom: calc(16rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}
.key-down{
		height: 50rpx;
		width: 50rpx;
		display: block;
		margin: 0 auto;
}
.number-box {
		flex: 3;
}
.number-box .key {
		float: left;
		margin: 16rpx 16rpx 0 0;
		width: calc(100% / 3 - 16rpx);
		height: 90rpx;
		border-radius: 10rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 40rpx;
		font-weight: bold;
		background-color: #fff;
}
.number-box .key.key-zero {
		width: calc((100% / 3) * 2 - 16rpx);
}
.keyboard .number-box-hover {
		/* 临时定义颜色 */
		background-color: #e1e1e1 !important;
}
.btn-box {
		flex: 1;
}
.btn-box .key {
		margin: 16rpx 16rpx 0 0;
		height: 90rpx;
		border-radius: 10rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 40rpx;
		font-weight: bold;
		background-color: #fff;
}
.btn-box .pay_btn {
		height: 298rpx;
		line-height: 298rpx;
		font-weight: normal;
		background-color: #1AAD19;
		color: #fff;
		font-size: 32rpx;
}
.btn-box .pay_btn.pay-btn-display {
		background-color: #9ED99D !important;
}
.btn-box .pay_btn.pay-btn-hover {
		background-color: #179B16;
}
.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}
.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.pstime-item .radio .radio-img {width: 100%;height: 100%}
.notice-box {
		padding: 30rpx 0;
		border-bottom: 1px solid #f0f0f0;
}
.notice-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
}
.notice-content {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
}

