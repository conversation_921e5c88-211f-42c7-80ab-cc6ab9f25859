
page.data-v-894e5e1c{
	background: #F4F5F7;
}


.banner{
	position: absolute;
	width: 100%;
	height: 300rpx;
	background: #4a8aff;
	/*border-radius: 0 0 20% 20%;*/
}
.page{
	padding: 70rpx 30rpx 0 30rpx;
}
.data{
	position: relative;
	background: #FFFFFF;
	padding: 40rpx 0 0 40rpx;
	border-radius: 12rpx;
	box-shadow:2px 0px 10px rgba(0,0,0,0.5);
	height:520rpx;
}
.data_info{
	display: flex;
	align-items: center;
	padding: 0 0 45rpx 0;
	border-bottom: 1rpx solid #eee;
	margin-bottom: 20rpx;
}
.data_head{
	width: 172rpx;
	height: 172rpx;
	border-radius: 50%;
	margin-right: 40rpx;
}
.data_name{
	font-size: 36rpx;
	font-family: Source Han Sans CN;
	font-weight: bold;
	color: #121212;
	padding-bottom: 10rpx;
}
.data_text{
	font-size: 24rpx;
	font-family: Aliba<PERSON> PuHuiTi;
	font-weight: 400;
	color: #545556;
	padding-top: 15rpx;
}
.data_list{
	padding: 9rpx 0;
	font-size: 28rpx;
	font-family: Alibaba PuHuiTi;
	font-weight: 400;
	color: #8B9198;
	display: flex;
}
.data_list ._img{
	height: 30rpx;
	width: 30rpx;
	margin: 5rpx 30rpx 0 0;
	flex-shrink:0;
}
.data_tag{
	position: absolute;
	top: 50rpx;
	right: 50rpx;
	height: 60rpx;
	width: 60rpx;
}
.module{
	position: relative;
	padding: 30rpx 10rpx;
	margin: 25rpx 0 0 0;
	background: #fff;
	display: flex;
	border-radius: 12rpx;
	box-shadow:2px 0px 10px #ccc;
}
.module_item{
	flex: 1;
}
.module_img{
	height: 72rpx;
	width: 72rpx;
	display: block;
	margin: 0 auto;
}
.module_text{
	font-size: 24rpx;
	text-align: center;
	font-family: Alibaba PuHuiTi;
	font-weight: 400;
	color: #8B9198;
	margin-top: 20rpx;
	line-height:30rpx;
}
.list{
	position: relative;
	padding: 40rpx;
	margin: 25rpx 0 0 0;
	background: #fff;
	border-radius: 12rpx;
	box-shadow:2px 0px 10px #ccc;
}
.list_title{
	font-size: 32rpx;
	font-family: Source Han Sans CN;
	font-weight: 500;
	color: #121212;
	padding-bottom:20rpx;
}
.list_item{
	display: flex;
	align-items: center;
	padding:22rpx 0;
}
.list_item:active{background:#f5f5f5}
.list_img{
	height: 48rpx;
	width: 48rpx;
	flex-shrink: 0;
}
.list_lable{
	font-size: 30rpx;
	font-family: Alibaba PuHuiTi;
	font-weight: 500;
	color: #353535;
	flex-shrink: 0;
	padding: 0 50rpx 0 25rpx;
	width:180rpx;
}
.list_value{
	font-size: 28rpx;
	font-family: Alibaba PuHuiTi;
	font-weight: 400;
	color: #232323;
	min-width: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.person{
	position: relative;
	padding: 40rpx;
	margin: 25rpx 0 0 0;
	background: #fff;
	border-radius: 12rpx;
	box-shadow:2px 0px 10px #ccc;
}
.person_title{
	font-size: 32rpx;
	font-family: Source Han Sans CN;
	font-weight: 500;
	color: #121212;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.person_title view{
	width: 200rpx;
	height: 1rpx;
	background: #EEEEEE;
}
.person_data{
	margin-top: 30rpx;
}
.opt{
	position: relative;
	width: 100%;
	height: 190rpx;
}
.opt_module{
	width: 100%;
	height: 190rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;
}
.opt_btn{
	width: 200rpx;
	height: 108rpx;
	background: #F2F6FF;
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #4A84FF;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 54rpx;
	box-shadow: 0 0 10rpx #e0e0e0;
}
.opt_btn:last-child{
	width: 450rpx;
	height: 108rpx;
	background: #4A84FF;
	border-radius: 54rpx;
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #FFFFFF;
}

