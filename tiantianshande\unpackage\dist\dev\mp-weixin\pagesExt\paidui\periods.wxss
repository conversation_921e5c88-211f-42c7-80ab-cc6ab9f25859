
.container {
  width: 100%;
  background-color: #f5f5f5;
}

/* 横幅样式 */
.banner {
  display: flex;
  width: 100%;
  height: 400rpx;
  padding: 40rpx 32rpx;
  color: #fff;
  position: relative;
}
.banner image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.banner .info {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding-top: 10rpx;
}
.banner .nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.banner .subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 统计信息样式 */
.stats-container {
  width: 100%;
  padding: 0 30rpx;
  margin-top: -180rpx;
  position: relative;
  margin-bottom: 20rpx;
}
.stats-card {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.stats-row:last-child {
  margin-bottom: 0;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 分期列表样式 */
.periods-content {
  width: 100%;
  padding: 0 30rpx;
}
.period-card {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.period-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}
.period-title {
  display: flex;
  flex-direction: column;
}
.period-id {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.period-num {
  font-size: 24rpx;
  color: #666;
}
.period-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.status-waiting {
  background: #fff7e6;
  color: #fa8c16;
}
.status-completed {
  background: #f6ffed;
  color: #52c41a;
}
.period-info {
  margin-bottom: 20rpx;
}
.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.info-row:last-child {
  margin-bottom: 0;
}
.info-item {
  display: flex;
  align-items: center;
  flex: 1;
}
.info-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}
.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}
.info-value.amount {
  color: #ff4d4f;
  font-weight: bold;
}
.period-progress {
  margin-bottom: 20rpx;
}
.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.progress-text {
  font-size: 24rpx;
  color: #666;
}
.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  transition: width 0.3s ease;
}
.period-actions {
  display: flex;
  justify-content: space-between;
}
.action-btn {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #f0f0f0;
  border-radius: 8rpx;
  margin: 0 10rpx;
  font-size: 26rpx;
  color: #666;
}
.action-btn:first-child {
  margin-left: 0;
}
.action-btn:last-child {
  margin-right: 0;
}
.action-btn:active {
  background: #e0e0e0;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.modal-content {
  width: 80%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  max-height: 80%;
  overflow-y: auto;
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}
.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
}
.modal-body {
  padding: 30rpx;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.detail-item:last-child {
  border-bottom: none;
}
.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}
.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}
.detail-value.amount {
  color: #ff4d4f;
  font-weight: bold;
}

