.container {
  padding: 30rpx 20rpx;
  background: #F8F9FD;
  min-height: 100vh;
}
.container .tab-box {
  display: flex;
  background: #fff;
  padding: 16rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}
.container .tab-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200%;
  background: linear-gradient(180deg, rgba(250, 81, 81, 0.08) 0%, rgba(250, 81, 81, 0) 100%);
  opacity: 0.5;
  pointer-events: none;
}
.container .tab-box .tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  padding: 24rpx 0;
  transition: all 0.3s ease;
}
.container .tab-box .tab-item.active {
  color: #FA5151;
  font-weight: 600;
  -webkit-transform: scale(1.02);
          transform: scale(1.02);
}
.container .tab-box .tab-item.active:after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 48rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #FA5151 0%, #FF7676 100%);
  border-radius: 6rpx;
}
.container .filter-box {
  background: #fff;
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.container .filter-box .filter-row {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.container .filter-box .filter-row .picker-item {
  flex: 1;
  height: 76rpx;
  background: #F8F9FD;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}
.container .filter-box .filter-row .picker-item:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  background: #F0F2F5;
}
.container .filter-box .filter-row .picker-item .icon-arrow {
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid #999;
  margin-left: 12rpx;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.container .rank-list {
  background: #fff;
  border-radius: 16rpx;
  padding: 8rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.container .rank-list .rank-item {
  display: flex;
  align-items: center;
  padding: 24rpx 16rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}
.container .rank-list .rank-item:last-child {
  border-bottom: none;
}
.container .rank-list .rank-item:hover {
  background: rgba(250, 81, 81, 0.02);
  -webkit-transform: translateX(4rpx);
          transform: translateX(4rpx);
}
.container .rank-list .rank-item .avatar-box {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  border-radius: 50%;
  padding: 4rpx;
  position: relative;
}
.container .rank-list .rank-item .avatar-box.rank-1 {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
}
.container .rank-list .rank-item .avatar-box.rank-2 {
  background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
  box-shadow: 0 4rpx 12rpx rgba(169, 169, 169, 0.3);
}
.container .rank-list .rank-item .avatar-box.rank-3 {
  background: linear-gradient(135deg, #CD7F32 0%, #B87333 100%);
  box-shadow: 0 4rpx 12rpx rgba(205, 127, 50, 0.3);
}
.container .rank-list .rank-item .avatar-box .avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f5f5f5;
}
.container .rank-list .rank-item .info {
  flex: 1;
  padding: 0 16rpx;
}
.container .rank-list .rank-item .info .name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}
.container .rank-list .rank-item .info .name.product-link {
  position: relative;
  display: inline-block;
  padding-bottom: 4rpx;
}
.container .rank-list .rank-item .info .name.product-link:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2rpx;
  background: currentColor;
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  -webkit-transform-origin: right;
          transform-origin: right;
}
.container .rank-list .rank-item .info .name.product-link:active {
  opacity: 0.8;
}
.container .rank-list .rank-item .info .name.product-link:hover:after {
  -webkit-transform: scaleX(1);
          transform: scaleX(1);
  -webkit-transform-origin: left;
          transform-origin: left;
}
.container .rank-list .rank-item .info .name.rank-1 {
  color: #FFD700;
  font-weight: 600;
}
.container .rank-list .rank-item .info .name.rank-2 {
  color: #C0C0C0;
  font-weight: 600;
}
.container .rank-list .rank-item .info .name.rank-3 {
  color: #CD7F32;
  font-weight: 600;
}
.container .rank-list .rank-item .info .level,
.container .rank-list .rank-item .info .shop-name {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}
.container .rank-list .rank-item .info .level::before,
.container .rank-list .rank-item .info .shop-name::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  background: #FA5151;
  border-radius: 50%;
  margin-right: 8rpx;
  opacity: 0.5;
}
.container .rank-list .rank-item .data {
  text-align: right;
  padding-left: 24rpx;
}
.container .rank-list .rank-item .data .num {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
  background: linear-gradient(90deg, #FA5151 0%, #FF7676 100%);
  -webkit-background-clip: text;
  color: transparent;
}
.container .loading-more,
.container .no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 32rpx 0;
  letter-spacing: 2rpx;
}
.container .loading-more::before, .container .loading-more::after, .container .no-more::before, .container .no-more::after {
  content: '';
  display: inline-block;
  width: 80rpx;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ddd);
  margin: 0 20rpx;
  vertical-align: middle;
}
.container .loading-more::after,
.container .no-more::after {
  background: linear-gradient(90deg, #ddd, transparent);
}
.container .rank-num {
  width: 64rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  margin-right: 24rpx;
  border-radius: 50%;
  background: #F8F9FD;
  color: #999;
  position: relative;
  transition: all 0.3s ease;
}
.container .rank-num .num {
  position: relative;
  z-index: 2;
}
.container .rank-num .crown {
  position: absolute;
  top: -16rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 32rpx;
  height: 24rpx;
  background: url('data:image/svg+xml;utf8,<svg t="1709799047439" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4286"><path d="M910.8 405.8L814 492.9 755.3 333c-2.4-6.5-7.6-11.6-14.1-13.7-6.5-2.2-13.7-1.1-19.3 2.9L548.1 450 430.1 161.5c-3.2-7.9-10.9-13.1-19.5-13.1s-16.2 5.2-19.5 13.1L273.3 450l-174-127.9c-5.7-4.1-12.8-5.1-19.3-2.9-6.5 2.2-11.7 7.2-14.1 13.7L7.2 492.9l-96.8-87.1c-5.4-4.9-13-6.3-19.8-3.8-6.7 2.6-11.6 8.5-12.8 15.6-19.2 113.6 6.1 230.4 69.9 321.2 63.8 90.8 159.4 153.8 268.9 177.2 20.8 4.4 42 7.5 63.1 9.2 17.7 1.4 35.5 2.1 53.2 2.1 17.8 0 35.6-0.7 53.3-2.1 21.2-1.7 42.3-4.8 63.1-9.2 109.5-23.4 205.1-86.4 268.9-177.2 63.8-90.8 89.1-207.6 69.9-321.2-1.2-7.1-6.1-13.1-12.8-15.6-6.8-2.5-14.4-1.1-19.8 3.8z" fill="%23FFD700" p-id="4287"></path></svg>') no-repeat center/contain;
  z-index: 1;
}
.container .rank-num.rank-1 {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.container .rank-num.rank-1::after {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.2) 100%);
  z-index: 0;
  -webkit-animation: pulse 1.5s ease-in-out infinite;
          animation: pulse 1.5s ease-in-out infinite;
}
.container .rank-num.rank-2 {
  background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(169, 169, 169, 0.3);
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.container .rank-num.rank-3 {
  background: linear-gradient(135deg, #CD7F32 0%, #B87333 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(205, 127, 50, 0.3);
  -webkit-transform: scale(1.02);
          transform: scale(1.02);
}
@-webkit-keyframes pulse {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
100% {
    -webkit-transform: scale(1.5);
            transform: scale(1.5);
    opacity: 0;
}
}
@keyframes pulse {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
100% {
    -webkit-transform: scale(1.5);
            transform: scale(1.5);
    opacity: 0;
}
}
.container .info {
  flex: 1;
  padding: 0 16rpx;
}
.container .info .name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}
.container .info .name.product-link {
  position: relative;
  display: inline-block;
  padding-bottom: 4rpx;
}
.container .info .name.product-link:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2rpx;
  background: currentColor;
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  -webkit-transform-origin: right;
          transform-origin: right;
}
.container .info .name.product-link:active {
  opacity: 0.8;
}
.container .info .name.product-link:hover:after {
  -webkit-transform: scaleX(1);
          transform: scaleX(1);
  -webkit-transform-origin: left;
          transform-origin: left;
}
.container .info .name.rank-1 {
  color: #FFD700;
  font-weight: 600;
}
.container .info .name.rank-2 {
  color: #C0C0C0;
  font-weight: 600;
}
.container .info .name.rank-3 {
  color: #CD7F32;
  font-weight: 600;
}

