
.container { padding: 20rpx;
}
.item { width: 94%; margin: 0 3%; padding: 20rpx; background: #fff; margin-top: 20rpx; border-radius: 20rpx;
}
.product-item2 { display: flex; padding: 20rpx 0; border-bottom: 1px solid #E6E6E6;
}
.product-pic { width: 180rpx; height: 180rpx; background: #ffffff; overflow: hidden;
}
.product-pic image { width: 100%; height: 100%;
}
.product-info { flex: 1; padding: 5rpx 10rpx;
}
.p1 { word-break: break-all; text-overflow: ellipsis; overflow: hidden; height: 80rpx; line-height: 40rpx; font-size: 30rpx; color: #111;
}
.p2 { font-size: 32rpx; height: 40rpx; line-height: 40rpx;
}
.p2 .t2 { margin-left: 10rpx; font-size: 26rpx; color: #888; text-decoration: line-through;
}
.p3 { font-size: 24rpx; height: 50rpx; line-height: 50rpx; overflow: hidden;
}
.p3 .t1 { color: #aaa; font-size: 24rpx;
}
.p3 .t2 { color: #888; font-size: 24rpx;
}
.foot { display: flex; align-items: center; width: 100%; height: 100rpx; line-height: 100rpx; color: #999; font-size: 24rpx;
}
.no-data { text-align: center; color: #999; font-size: 24rpx; margin-top: 50rpx;
}

