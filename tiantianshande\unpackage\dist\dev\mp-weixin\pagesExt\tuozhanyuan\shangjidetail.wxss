
.address { display: flex; align-items: center; width: 100%; padding: 20rpx 3%; background: #FFF; margin-bottom: 20rpx;
}
.address .img { width: 60rpx;
}
.address image { width: 50rpx; height: 50rpx;
}
.address .info { flex: 1; display: flex; flex-direction: column;
}
.address .info .t1 { font-weight: bold;
}
.product { width: 100%; padding: 14rpx 3%; background: #FFF;
}
.product .content { display: flex; position: relative; width: 100%; padding: 16rpx 0px; border-bottom: 1px #e5e5e5 dashed; position: relative;
}
.product .content:last-child { border-bottom: 0;
}
.product .content image { width: 140rpx; height: 140rpx;
}
.product .content .detail { display: flex; flex-direction: column; margin-left: 14rpx; flex: 1;
}
.product .content .detail .t1 { height: 60rpx; line-height: 30rpx; color: #000;
}
.product .content .detail .t2 { height: 46rpx; line-height: 46rpx; color: #999; overflow: hidden; font-size: 26rpx;
}
.product .content .detail .t3 { display: flex; height: 30rpx; line-height: 30rpx; color: #ff4246;
}
.product .content .detail .x1 { flex: 1;
}
.product .content .detail .x2 { width: 100rpx; font-size: 32rpx; text-align: right; margin-right: 8rpx;
}
.product .content .comment { position: absolute; top: 64rpx; right: 10rpx; border: 1px #ffc702 solid; border-radius: 10rpx; background: #fff; color: #ffc702; padding: 0 10rpx; height: 46rpx; line-height: 46rpx;
}
.orderinfo { width: 94%; margin: 20rpx 3%; border-radius: 16rpx; padding: 14rpx 3%; background: #FFF;
}
.orderinfo .item { display: flex; width: 100%; padding: 20rpx 0; border-bottom: 1px dashed #ededed;
}
.orderinfo .item:last-child { border-bottom: 0;
}
.orderinfo .item .t1 { width: 200rpx;
}
.orderinfo .item .t2 { flex: 1; text-align: right;
}
.orderinfo .item .red { color: red;
}
.bottom { width: 100%; padding: 16rpx 20rpx; background: #fff; position: fixed; bottom: 0px; left: 0px; display: flex; justify-content: flex-end; align-items: center;
}
.bottom .btn { border-radius: 10rpx; padding: 10rpx 16rpx; margin-left: 10px; border: 1px #999 solid;
}
.uni-popup-dialog { width: 300px; border-radius: 5px; background-color: #fff;
}
.uni-dialog-title { display: flex; flex-direction: row; justify-content: center; padding-top: 15px; padding-bottom: 5px;
}
.uni-dialog-title-text { font-size: 16px; font-weight: 500;
}
.uni-dialog-content { display: flex; flex-direction: row; justify-content: center; align-items: center; padding: 5px 15px 15px 15px; width: 100%;
}
.uni-dialog-content-text { font-size: 14px; color: #6e6e6e;
}
.uni-dialog-button-group { display: flex; flex-direction: row; border-top-color: #f5f5f5; border-top-style: solid; border-top-width: 1px;
}
.uni-dialog-button { display: flex; flex: 1; flex-direction: row; justify-content: center; align-items: center; height: 45px;
}
.uni-border-left { border-left-color: #f0f0f0; border-left-style: solid; border-left-width: 1px;
}
.uni-dialog-button-text { font-size: 14px;
}
.uni-button-color { color: #007aff;
}

