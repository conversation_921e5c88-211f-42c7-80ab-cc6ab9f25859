






























































































































































































































































































































/* 容器样式 */
.stats-container {
  padding: 24rpx;
  background: #f5f6fa;
}
/* 统计卡片 */
.stats-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}
.card-header {
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
/* 统计网格 */
.stats-grid {
  width: 100%;
}
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.stats-row:last-child {
  margin-bottom: 0;
  border-bottom: none;
}
/* 统计项 */
.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 8rpx;
}
.stats-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.stats-value.highlight {
  color: #fe2b2e;
}
.stats-label {
  font-size: 24rpx;
  color: #666;
}


