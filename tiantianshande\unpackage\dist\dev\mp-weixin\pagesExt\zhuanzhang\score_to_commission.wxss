
.container {
	display: flex;
	flex-direction: column
}
.mymoney {
	width: 94%;
	margin: 20rpx 3%;
	border-radius: 10rpx 56rpx 10rpx 10rpx;
	position: relative;
	display: flex;
	flex-direction: column;
	padding: 70rpx 0
}
.mymoney .f1 {
	margin: 0 0 0 60rpx;
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
}
.mymoney .f2 {
	margin: 20rpx 0 0 60rpx;
	color: #fff;
	font-size: 64rpx;
	font-weight: bold
}
.mymoney .f3 {
	height: 56rpx;
	padding: 0 10rpx 0 20rpx;
	border-radius: 28rpx 0px 0px 28rpx;
	background: rgba(255, 255, 255, 0.2);
	font-size: 20rpx;
	font-weight: bold;
	color: #fff;
	display: flex;
	align-items: center;
	position: absolute;
	top: 94rpx;
	right: 0
}
.content2 {
	width: 94%;
	margin: 10rpx 3%;
	border-radius: 10rpx;
	display: flex;
	flex-direction: column;
	background: #fff
}
.balance-info {
	display: flex;
	padding: 30rpx;
	border-bottom: 1px solid #F0F0F0;
}
.balance-item {
	flex: 1;
	text-align: center;
}
.balance-label {
	display: block;
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}
.balance-value {
	display: block;
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}
.rules-section {
	padding: 30rpx;
	border-bottom: 1px solid #F0F0F0;
}
.rules-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 20rpx;
}
.rules-content {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 10rpx;
}
.rule-item {
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 8rpx;
}
.rule-item:last-child {
	margin-bottom: 0;
}
.preview-section {
	padding: 20rpx 30rpx;
	background: #f0f9ff;
	border-bottom: 1px solid #F0F0F0;
}
.preview-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}
.preview-item:last-child {
	margin-bottom: 0;
}
.preview-label {
	font-size: 26rpx;
	color: #666;
}
.preview-value {
	font-size: 28rpx;
	color: #007AFF;
	font-weight: bold;
}
.preview-value.fee {
	color: #ff6b6b;
}
.content2 .item3 {
	display: flex;
	width: 100%;
	padding: 0 30rpx;
	border-bottom: 1px solid #F0F0F0;
}
.content2 .item3 .f2 {
	display: flex;
	align-items: center;
	font-size: 60rpx;
	color: #333333;
	font-weight: bold;
	width: 100%;
}
.content2 .item3 .f2 input {
	height: 120rpx;
	line-height: 120rpx;
	width: 100%;
}
.op {
	width: 96%;
	margin: 20rpx 2%;
	display: flex;
	align-items: center;
	margin-top: 40rpx
}
.op .btn {
	flex: 1;
	height: 100rpx;
	line-height: 100rpx;
	background: #07C160;
	width: 90%;
	margin: 0 10rpx;
	border-radius: 10rpx;
	color: #fff;
	font-size: 28rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center
}

