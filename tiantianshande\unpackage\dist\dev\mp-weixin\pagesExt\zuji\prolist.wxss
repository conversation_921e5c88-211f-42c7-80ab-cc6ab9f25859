
.search-container {
	position: fixed;
	width: 100%;
	background: #fff;
	z-index: 9;
	top: 0px
}
.topsearch {
	width: 100%;
	padding: 16rpx 20rpx;
}
.topsearch .f1 {
	height: 60rpx;
	border-radius: 30rpx;
	border: 0;
	background-color: #f7f7f7;
	flex: 1
}
.topsearch .f1 .img {
	width: 24rpx;
	height: 24rpx;
	margin-left: 10px
}
.topsearch .f1 input {
	height: 100%;
	flex: 1;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
}
.topsearch .search-btn {
	display: flex;
	align-items: center;
	color: #5a5a5a;
	font-size: 30rpx;
	width: 60rpx;
	text-align: center;
	margin-left: 20rpx
}
.search-navbar {
	display: flex;
	text-align: center;
	align-items: center;
	padding: 5rpx 0
}
.search-navbar-item {
	flex: 1;
	height: 70rpx;
	line-height: 70rpx;
	position: relative;
	font-size: 28rpx;
	font-weight: bold;
	color: #323232
}
.search-navbar-item .iconshangla {
	position: absolute;
	top: -4rpx;
	padding: 0 6rpx;
	font-size: 20rpx;
	color: #7D7D7D
}
.search-navbar-item .icondaoxu {
	position: absolute;
	top: 8rpx;
	padding: 0 6rpx;
	font-size: 20rpx;
	color: #7D7D7D
}
.search-navbar-item .iconshaixuan {
	margin-left: 10rpx;
	font-size: 22rpx;
	color: #7d7d7d
}
.search-history {
	padding: 24rpx 34rpx;
}
.search-history .search-history-title {
	color: #666;
}
.search-history .delete-search-history {
	float: right;
	padding: 15rpx 20rpx;
	margin-top: -15rpx;
}
.search-history-list {
	padding: 24rpx 0 0 0;
}
.search-history-list .search-history-item {
	display: inline-block;
	height: 50rpx;
	line-height: 50rpx;
	padding: 0 20rpx;
	margin: 0 10rpx 10rpx 0;
	background: #ddd;
	border-radius: 10rpx;
	font-size: 26rpx;
}
.filter-scroll-view {
	margin-top: 0px
}
.search-filter {
	display: flex;
	flex-direction: column;
	text-align: left;
	width: 100%;
	flex-wrap: wrap;
	padding: 0;
}
.filter-content-title {
	color: #999;
	font-size: 28rpx;
	height: 30rpx;
	line-height: 30rpx;
	padding: 0 30rpx;
	margin-top: 30rpx;
	margin-bottom: 10rpx
}
.filter-title {
	color: #BBBBBB;
	font-size: 32rpx;
	background: #F8F8F8;
	padding: 60rpx 0 30rpx 20rpx;
}
.search-filter-content {
	display: flex;
	flex-wrap: wrap;
	padding: 10rpx 20rpx;
}
.search-filter-content .filter-item {
	background: #F4F4F4;
	border-radius: 28rpx;
	color: #2B2B2B;
	font-weight: bold;
	margin: 10rpx 10rpx;
	min-width: 140rpx;
	height: 56rpx;
	line-height: 56rpx;
	text-align: center;
	font-size: 24rpx;
	padding: 0 30rpx
}
.search-filter-content .close {
	text-align: right;
	font-size: 24rpx;
	color: #ff4544;
	width: 100%;
	padding-right: 20rpx
}
.search-filter button .icon {
	margin-top: 6rpx;
	height: 54rpx;
}
.search-filter-btn {
	display: flex;
	padding: 30rpx 30rpx;
	justify-content: space-between
}
.search-filter-btn .btn {
	width: 240rpx;
	height: 66rpx;
	line-height: 66rpx;
	background: #fff;
	border: 1px solid #e5e5e5;
	border-radius: 33rpx;
	color: #2B2B2B;
	font-weight: bold;
	font-size: 24rpx;
	text-align: center
}
.search-filter-btn .btn2 {
	width: 240rpx;
	height: 66rpx;
	line-height: 66rpx;
	border-radius: 33rpx;
	color: #fff;
	font-weight: bold;
	font-size: 24rpx;
	text-align: center
}
.product-container {
	width: 100%;
	margin-top: 190rpx;
	font-size: 26rpx;
	padding: 0 24rpx
}

