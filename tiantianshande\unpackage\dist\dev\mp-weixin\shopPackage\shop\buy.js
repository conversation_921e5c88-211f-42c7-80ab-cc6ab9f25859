(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["shopPackage/shop/buy"],{

/***/ 189:
/*!*****************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/main.js?{"page":"shopPackage%2Fshop%2Fbuy"} ***!
  \*****************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _buy = _interopRequireDefault(__webpack_require__(/*! ./shopPackage/shop/buy.vue */ 190));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_buy.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 190:
/*!**********************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue ***!
  \**********************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buy.vue?vue&type=template&id=f0b13d8c& */ 191);
/* harmony import */ var _buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buy.vue?vue&type=script&lang=js& */ 193);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./buy.vue?vue&type=style&index=0&lang=css& */ 195);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 34);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__["render"],
  _buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "shopPackage/shop/buy.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 191:
/*!*****************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?vue&type=template&id=f0b13d8c& ***!
  \*****************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=template&id=f0b13d8c& */ 192);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_f0b13d8c___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 192:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?vue&type=template&id=f0b13d8c& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    couponlist: function () {
      return __webpack_require__.e(/*! import() | components/couponlist/couponlist */ "components/couponlist/couponlist").then(__webpack_require__.bind(null, /*! @/components/couponlist/couponlist.vue */ 7922))
    },
    uniDataPicker: function () {
      return Promise.all(/*! import() | components/uni-data-picker/uni-data-picker */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-data-picker/uni-data-picker")]).then(__webpack_require__.bind(null, /*! @/components/uni-data-picker/uni-data-picker.vue */ 7876))
    },
    loading: function () {
      return __webpack_require__.e(/*! import() | components/loading/loading */ "components/loading/loading").then(__webpack_require__.bind(null, /*! @/components/loading/loading.vue */ 5109))
    },
    dpTabbar: function () {
      return __webpack_require__.e(/*! import() | components/dp-tabbar/dp-tabbar */ "components/dp-tabbar/dp-tabbar").then(__webpack_require__.bind(null, /*! @/components/dp-tabbar/dp-tabbar.vue */ 5116))
    },
    popmsg: function () {
      return __webpack_require__.e(/*! import() | components/popmsg/popmsg */ "components/popmsg/popmsg").then(__webpack_require__.bind(null, /*! @/components/popmsg/popmsg.vue */ 5123))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l6 = _vm.isload
    ? _vm.__map(_vm.allbuydata, function (buydata, index) {
        var $orig = _vm.__get_orig(buydata)
        var l0 = _vm.__map(buydata.prodata, function (item, index2) {
          var $orig = _vm.__get_orig(item)
          var m0 = item.product.glassrecord ? _vm.t("color1rgb") : null
          return {
            $orig: $orig,
            m0: m0,
          }
        })
        var l1 = _vm.__map(buydata.freightList, function (item, idx2) {
          var $orig = _vm.__get_orig(item)
          var m1 = buydata.freightkey == idx2 ? _vm.t("color1") : null
          var m2 = buydata.freightkey == idx2 ? _vm.t("color1rgb") : null
          return {
            $orig: $orig,
            m1: m1,
            m2: m2,
          }
        })
        var g0 =
          buydata.freightList[buydata.freightkey].minpriceset == 1 &&
          buydata.freightList[buydata.freightkey].minprice > 0 &&
          buydata.freightList[buydata.freightkey].minprice * 1 >
            buydata.product_price * 1
            ? (
                buydata.freightList[buydata.freightkey].minprice -
                buydata.product_price
              ).toFixed(2)
            : null
        var l2 =
          buydata.freightList[buydata.freightkey].pstype == 1
            ? _vm.__map(
                buydata.freightList[buydata.freightkey].storedata,
                function (item, idx) {
                  var $orig = _vm.__get_orig(item)
                  var m3 =
                    (idx < 5 || _vm.storeshowall == true) &&
                    buydata.freightList[buydata.freightkey].storekey == idx
                      ? _vm.t("color1")
                      : null
                  return {
                    $orig: $orig,
                    m3: m3,
                  }
                }
              )
            : null
        var g1 =
          buydata.freightList[buydata.freightkey].pstype == 1
            ? _vm.storeshowall == false &&
              buydata.freightList[buydata.freightkey].storedata.length > 5
            : null
        var l3 =
          buydata.freightList[buydata.freightkey].pstype == 5
            ? _vm.__map(
                buydata.freightList[buydata.freightkey].storedata,
                function (item, idx) {
                  var $orig = _vm.__get_orig(item)
                  var m4 =
                    (idx < 5 || _vm.storeshowall == true) &&
                    buydata.freightList[buydata.freightkey].storekey == idx
                      ? _vm.t("color1")
                      : null
                  return {
                    $orig: $orig,
                    m4: m4,
                  }
                }
              )
            : null
        var g2 =
          buydata.freightList[buydata.freightkey].pstype == 5
            ? _vm.storeshowall == false &&
              buydata.freightList[buydata.freightkey].storedata.length > 5
            : null
        var m5 = buydata.leveldk_money > 0 ? _vm.t("会员") : null
        var m6 = _vm.t("优惠券")
        var g3 = buydata.couponCount > 0 ? buydata.coupons.length : null
        var l4 =
          buydata.couponCount > 0 && g3 > 0
            ? _vm.__map(buydata.coupons, function (item, index) {
                var $orig = _vm.__get_orig(item)
                var m7 = _vm.t("color1")
                return {
                  $orig: $orig,
                  m7: m7,
                }
              })
            : null
        var m8 = buydata.couponCount > 0 && !(g3 > 0) ? _vm.t("color1") : null
        var m9 = !(buydata.couponCount > 0) ? _vm.t("优惠券") : null
        var g4 =
          buydata.cuxiaoCount > 0
            ? buydata.cuxiaonameArr && buydata.cuxiaonameArr.length > 0
            : null
        var l5 =
          buydata.cuxiaoCount > 0 && g4
            ? _vm.__map(buydata.cuxiaonameArr, function (item, index) {
                var $orig = _vm.__get_orig(item)
                var m10 = _vm.t("color1")
                return {
                  $orig: $orig,
                  m10: m10,
                }
              })
            : null
        var m11 = buydata.cuxiaoCount > 0 && !g4 ? _vm.t("color1") : null
        return {
          $orig: $orig,
          l0: l0,
          l1: l1,
          g0: g0,
          l2: l2,
          g1: g1,
          l3: l3,
          g2: g2,
          m5: m5,
          m6: m6,
          g3: g3,
          l4: l4,
          m8: m8,
          m9: m9,
          g4: g4,
          l5: l5,
          m11: m11,
        }
      })
    : null
  var m12 =
    _vm.isload &&
    _vm.userinfo.score2money > 0 &&
    (_vm.userinfo.scoremaxtype == 0 ||
      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))
      ? _vm.t("积分")
      : null
  var m13 =
    _vm.isload &&
    _vm.userinfo.score2money > 0 &&
    (_vm.userinfo.scoremaxtype == 0 ||
      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))
      ? _vm.t("积分")
      : null
  var m14 =
    _vm.isload &&
    _vm.userinfo.score2moneyhei > 0 &&
    (_vm.userinfo.scoremaxtypehei == 0 ||
      (_vm.userinfo.scoremaxtypehei == 1 &&
        _vm.userinfo.scoredkmaxmoneyhei > 0))
      ? _vm.t("现金券")
      : null
  var m15 =
    _vm.isload &&
    _vm.userinfo.score2moneyhei > 0 &&
    (_vm.userinfo.scoremaxtypehei == 0 ||
      (_vm.userinfo.scoremaxtypehei == 1 &&
        _vm.userinfo.scoredkmaxmoneyhei > 0))
      ? _vm.t("现金券")
      : null
  var m16 =
    _vm.isload &&
    _vm.userinfo.score2moneyyu > 0 &&
    (_vm.userinfo.scoremaxtypeyu == 0 ||
      (_vm.userinfo.scoremaxtypeyu == 1 && _vm.userinfo.scoredkmaxmoneyyu > 0))
      ? _vm.t("余额")
      : null
  var m17 =
    _vm.isload &&
    _vm.userinfo.score2moneyyu > 0 &&
    (_vm.userinfo.scoremaxtypeyu == 0 ||
      (_vm.userinfo.scoremaxtypeyu == 1 && _vm.userinfo.scoredkmaxmoneyyu > 0))
      ? _vm.t("余额")
      : null
  var m18 =
    _vm.isload &&
    _vm.userinfo.score2moneyhuang > 0 &&
    (_vm.userinfo.scoremaxtypehuang == 0 ||
      (_vm.userinfo.scoremaxtypehuang == 1 &&
        _vm.userinfo.scoredkmaxmoneyhuang > 0))
      ? _vm.t("红包")
      : null
  var m19 =
    _vm.isload &&
    _vm.userinfo.score2moneyhuang > 0 &&
    (_vm.userinfo.scoremaxtypehuang == 0 ||
      (_vm.userinfo.scoremaxtypehuang == 1 &&
        _vm.userinfo.scoredkmaxmoneyhuang > 0))
      ? _vm.t("红包")
      : null
  var m20 = _vm.isload ? _vm.t("color1") : null
  var m21 = _vm.isload ? _vm.t("color1rgb") : null
  var m22 =
    _vm.isload && _vm.invoiceShow ? _vm.inArray(1, _vm.invoice_type) : null
  var m23 =
    _vm.isload && _vm.invoiceShow ? _vm.inArray(2, _vm.invoice_type) : null
  var m24 = _vm.isload && _vm.invoiceShow ? _vm.t("color1") : null
  var m25 = _vm.isload && _vm.couponvisible ? _vm.t("优惠券") : null
  var l7 =
    _vm.isload && _vm.pstimeDialogShow
      ? _vm.__map(
          _vm.allbuydata[_vm.nowbid].freightList[
            _vm.allbuydata[_vm.nowbid].freightkey
          ].pstimeArr,
          function (item, index) {
            var $orig = _vm.__get_orig(item)
            var m26 =
              _vm.allbuydata[_vm.nowbid].freight_time == item.value
                ? _vm.t("color1")
                : null
            return {
              $orig: $orig,
              m26: m26,
            }
          }
        )
      : null
  var m27 =
    _vm.isload && _vm.cuxiaovisible && _vm.multi_promotion && _vm.cxid === 0
      ? _vm.t("color1")
      : null
  var l8 =
    _vm.isload && _vm.cuxiaovisible && _vm.multi_promotion
      ? _vm.__map(_vm.allbuydata[_vm.bid].cuxiaolist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var g5 = _vm.cxids.indexOf(item.id)
          var m28 = g5 !== -1 ? _vm.t("color1") : null
          return {
            $orig: $orig,
            g5: g5,
            m28: m28,
          }
        })
      : null
  var m29 =
    _vm.isload && _vm.cuxiaovisible && !_vm.multi_promotion && _vm.cxid == 0
      ? _vm.t("color1")
      : null
  var l9 =
    _vm.isload && _vm.cuxiaovisible && !_vm.multi_promotion
      ? _vm.__map(_vm.allbuydata[_vm.bid].cuxiaolist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m30 = _vm.cxid == item.id ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m30: m30,
          }
        })
      : null
  var g6 =
    _vm.isload && _vm.cuxiaovisible
      ? _vm.cuxiaoList && _vm.cuxiaoList.info && _vm.cuxiaoList.info.length > 0
      : null
  var m31 = _vm.isload && _vm.cuxiaovisible ? _vm.t("color1") : null
  var l10 =
    _vm.isload && _vm.type11visible
      ? _vm.__map(
          _vm.allbuydata[_vm.bid].freightList[
            _vm.allbuydata[_vm.bid].freightkey
          ].type11pricedata,
          function (item, index) {
            var $orig = _vm.__get_orig(item)
            var m32 =
              _vm.address.id &&
              _vm.address.province == item.province &&
              _vm.address.city == item.city &&
              _vm.address.district == item.area &&
              _vm.type11key == index
                ? _vm.t("color1")
                : null
            return {
              $orig: $orig,
              m32: m32,
            }
          }
        )
      : null
  var m33 = _vm.isload && _vm.type11visible ? _vm.t("color1") : null
  var l11 =
    _vm.isload && _vm.membervisible
      ? _vm.__map(_vm.memberList, function (item2, i) {
          var $orig = _vm.__get_orig(item2)
          var m34 = _vm.checkMem.id == item2.id ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m34: m34,
          }
        })
      : null
  var l12 =
    _vm.isload && _vm.isshowglass
      ? _vm.__map(_vm.glassrecordlist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m35 = _vm.t("color1")
          return {
            $orig: $orig,
            m35: m35,
          }
        })
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l6: l6,
        m12: m12,
        m13: m13,
        m14: m14,
        m15: m15,
        m16: m16,
        m17: m17,
        m18: m18,
        m19: m19,
        m20: m20,
        m21: m21,
        m22: m22,
        m23: m23,
        m24: m24,
        m25: m25,
        l7: l7,
        m27: m27,
        l8: l8,
        m29: m29,
        l9: l9,
        g6: g6,
        m31: m31,
        l10: l10,
        m33: m33,
        l11: l11,
        l12: l12,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 193:
/*!***********************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js& */ 194);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 194:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var _default = {
  data: function data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      pre_url: app.globalData.pre_url,
      test: 'test',
      havetongcheng: 0,
      address: [],
      memberList: [],
      checkMem: {},
      usescore: 0,
      scoredk_money: 0,
      totalprice: '0.00',
      couponvisible: false,
      cuxiaovisible: false,
      membervisible: false,
      memberinfovisible: false,
      selectmemberinfo: {},
      bid: 0,
      nowbid: 0,
      needaddress: 1,
      linkman: '',
      singleFreight: 0,
      totalFreight: 0,
      tel: '',
      userinfo: {},
      pstimeDialogShow: false,
      pstimeIndex: -1,
      manjian_money: 0,
      cxid: 0,
      cxids: [],
      latitude: "",
      longitude: "",
      allbuydata: {},
      allbuydatawww: {},
      alltotalprice: "",
      cuxiaoinfo: false,
      cuxiaoList: {},
      type11visible: false,
      type11key: -1,
      regiondata: '',
      items: [],
      editorFormdata: [],
      buy_selectmember: false,
      multi_promotion: 0,
      storeshowall: false,
      order_change_price: false,
      invoiceShow: false,
      invoice: {},
      invoice_type: [],
      invoice_type_select: 1,
      name_type_select: 1,
      name_type_personal_disabled: false,
      inputDisabled: false,
      submitDisabled: false,
      pstype3needAddress: false,
      isshowglass: false,
      glassrecordlist: [],
      grid: 0,
      curindex: -1,
      curindex2: -1,
      is_yh: 0,
      //是否存在优惠
      yh_prices: 0,
      yh_nums: 0,
      newArr: []
    };
  },
  onLoad: function onLoad(opt) {
    this.opt = app.getopts(opt);
    this.getdata();
    this.newArr = [];
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.getdata();
  },
  methods: {
    getdata: function getdata() {
      var that = this;
      that.loading = true;
      app.get('ApiShop/buy', {
        prodata: that.opt.prodata
      }, function (res) {
        // console.log('返回信息');
        // console.log(res);
        that.loading = false;
        if (res.status == 0) {
          if (res.msg) {
            app.alert(res.msg, function () {
              if (res.url) {
                app.goto(res.url);
              } else {
                app.goback();
              }
            });
          } else if (res.url) {
            app.goto(res.url);
          } else {
            app.alert('您没有权限购买该订单');
          }
          return;
        }
        that.havetongcheng = res.havetongcheng;
        that.address = res.address;
        that.linkman = res.linkman;
        that.singleFreight = res.single_freight;
        that.totalFreight = res.total_freight;
        that.tel = res.tel;
        that.userinfo = res.userinfo;
        that.buy_selectmember = res.buy_selectmember;
        that.order_change_price = res.order_change_price;
        that.pstype3needAddress = res.pstype3needAddress;
        if (that.buy_selectmember) {
          uni.request({
            url: app.globalData.pre_url + '/static/area2.json',
            data: {},
            method: 'GET',
            header: {
              'content-type': 'application/json'
            },
            success: function success(res2) {
              that.items = res2.data;
            }
          });
        }
        that.allbuydata = res.allbuydata;
        that.allbuydatawww = JSON.parse(JSON.stringify(res.allbuydata));
        // console.log(that.allbuydata);
        // console.log(res.allbuydata);

        console.log(res.allbuydata);
        // 初始化 newArr
        that.newArr = [];
        that.is_yh = 0;

        // 确保 allbuydata 是数组
        if (Array.isArray(res.allbuydata)) {
          res.allbuydata.forEach(function (item, index) {
            if (item && item.prodata) {
              var obj = item.prodata;
              for (var key in obj) {
                if (obj[key].yh_danshu == 1 && that.newArr.length == 0) {
                  console.log(obj[key]);
                  that.is_yh = 1;
                  that.newArr.push({
                    "product_id": obj[key].id,
                    "is_yh": obj[key].yh_danshu,
                    "yh_num": obj[key].yh_danshu,
                    "yh_price": obj[key].yh_price
                  });
                }
              }
            } else {
              console.warn('res.allbuydata[' + index + '] 未定义或没有 prodata');
            }
          });
        } else {
          console.error('res.allbuydata 不是数组或为空');
        }
        // console.log('222');
        console.log(that.newArr);
        console.log('结束');
        that.needLocation = res.needLocation;
        that.scorebdkyf = res.scorebdkyf;
        that.scorebdkyfheihei = res.scorebdkyfheihei;
        that.multi_promotion = res.multi_promotion;
        that.calculatePrice();
        that.loaded();

        // var allbuydata = that.allbuydata;
        // for (var i in allbuydata) {
        // 	allbuydata[i].tempInvoice = uni.getStorageSync('temp_invoice_' + allbuydata[i].bid);
        // }
        // that.allbuydata = allbuydata;

        if (res.needLocation == 1) {
          app.getLocation(function (res) {
            var latitude = res.latitude;
            var longitude = res.longitude;
            that.latitude = latitude;
            that.longitude = longitude;
            var allbuydata = that.allbuydata;
            for (var i in allbuydata) {
              var freightList = allbuydata[i].freightList;
              for (var j in freightList) {
                if (freightList[j].pstype == 1 || freightList[j].pstype == 5) {
                  var storedata = freightList[j].storedata;
                  if (storedata) {
                    for (var x in storedata) {
                      if (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {
                        var juli = that.getDistance(latitude, longitude, storedata[x].latitude, storedata[x].longitude);
                        storedata[x].juli = juli;
                      }
                    }
                    storedata.sort(function (a, b) {
                      return a["juli"] - b["juli"];
                    });
                    for (var x in storedata) {
                      if (storedata[x].juli) {
                        storedata[x].juli = storedata[x].juli + '千米';
                      }
                    }
                    console.log(storedata);
                    allbuydata[i].freightList[j].storedata = storedata;
                  }
                }
              }
            }
            that.allbuydata = allbuydata;
          });
        }
      });
    },
    //积分抵扣
    scoredk: function scoredk(e) {
      var usescore = e.detail.value[0];
      if (!usescore) usescore = 0;
      this.usescore = usescore;
      this.calculatePrice();
    },
    //黑积分抵扣
    scoredkhei: function scoredkhei(e) {
      var usescorehei = e.detail.value[0];
      if (!usescorehei) usescorehei = 0;
      this.usescorehei = usescorehei;
      console.log(this.usescorehei, 888);
      this.calculatePrice();
    },
    //黄积分抵扣
    scoredkhuang: function scoredkhuang(e) {
      var usescorehuang = e.detail.value[0];
      if (!usescorehuang) usescorehuang = 0;
      this.usescorehuang = usescorehuang;
      console.log(this.usescorehuang, 999);
      this.calculatePrice();
    },
    //余额抵扣
    scoredkyu: function scoredkyu(e) {
      var usescoreyu = e.detail.value[0];
      if (!usescoreyu) usescoreyu = 0;
      this.usescoreyu = usescoreyu;
      this.calculatePrice();
    },
    inputLinkman: function inputLinkman(e) {
      this.linkman = e.detail.value;
    },
    inputTel: function inputTel(e) {
      this.tel = e.detail.value;
    },
    inputfield: function inputfield(e) {
      var bid = e.currentTarget.dataset.bid;
      var field = e.currentTarget.dataset.field;
      allbuydata2[bid][field] = e.detail.value;
      this.allbuydata2 = allbuydata2;
    },
    //选择收货地址
    chooseAddress: function chooseAddress() {
      app.goto('/pages/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));
    },
    inputPrice: function inputPrice(e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      var index2 = e.currentTarget.dataset.index2;
      var allbuydata = that.allbuydata;
      var allbuydatawww = that.allbuydatawww;
      var oldprice = allbuydatawww[index]['prodata'][index2].guige.sell_price;
      if (e.detail.value == '' || parseFloat(e.detail.value) < parseFloat(oldprice)) {
        that.submitDisabled = true;
        app.error('不能小于原价:' + oldprice);
        return;
      }
      that.submitDisabled = false;
      allbuydata[index]['prodata'][index2].guige.sell_price = e.detail.value;
      allbuydata[index]['product_price'] = (e.detail.value * allbuydata[index]['prodata'][index2].num).toFixed(2);
      // allbuydata[index].prodatastr = allbuydata[index].prodatastr
      that.allbuydata = allbuydata;
      console.log(allbuydata[index]);
      that.calculatePrice();
    },
    //计算价格
    calculatePrice: function calculatePrice() {
      var that = this;
      var address = that.address;
      var allbuydata = that.allbuydata;
      var alltotalprice = 0;
      var allfreight_price = 0;
      var needaddress = 0;
      // console.log(allbuydata)
      for (var k in allbuydata) {
        var product_price = parseFloat(allbuydata[k].product_price);
        var diy_amount = parseFloat(allbuydata[k].diy_amount);
        var leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣
        var manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动
        var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 
        var cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动 
        var invoice_money = parseFloat(allbuydata[k].invoice_money); //+发票 
        //var diy_amount = parseFloat(allbuydata[k].diy_amount); //包装价格
        //算运费
        var freightdata = allbuydata[k].freightList[allbuydata[k].freightkey];
        var freight_price = freightdata['freight_price'];
        if (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {
          needaddress = 1;
        }
        if (that.pstype3needAddress && (freightdata.pstype == 3 || freightdata.pstype == 4 || freightdata.pstype == 5)) {
          needaddress = 1;
        }
        if (allbuydata[k].coupontype == 4) {
          freight_price = 0;
        }
        //var totalprice = product_price - leveldk_money - manjian_money - coupon_money + cuxiao_money;
        var totalprice = product_price - leveldk_money - manjian_money - coupon_money + cuxiao_money + diy_amount;
        if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费

        totalprice = totalprice + freight_price;
        allbuydata[k].freight_price = freight_price.toFixed(2);
        if (allbuydata[k].business.invoice && allbuydata[k].business.invoice_rate > 0 && allbuydata[k].tempInvoice) {
          var invoice_money = totalprice * parseFloat(allbuydata[k].business.invoice_rate) / 100;
          allbuydata[k].invoice_money = invoice_money.toFixed(2);
          totalprice = totalprice + invoice_money;
        }
        console.log('invoice_money');
        console.log(invoice_money);
        allbuydata[k].totalprice = totalprice.toFixed(2);
        alltotalprice += totalprice;
        alltotalprice += that.totalFreight;
        allfreight_price += freight_price;
      }
      that.needaddress = needaddress;
      if (that.usescore) {
        var scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣
      } else {
        var scoredk_money = 0;
      }
      if (that.usescorehei) {
        var scorebdkyfhei = parseFloat(that.userinfo.scorebdkyfhei); //-积分抵扣
      } else {
        var scorebdkyfhei = 0;
      }
      if (that.usescorehuang) {
        var scorebdkyfhuang = parseFloat(that.userinfo.scorebdkyfhuang); //-积分抵扣
      } else {
        var scorebdkyfhuang = 0;
      }
      if (that.usescoreyu) {
        var scorebdkyfyu = parseFloat(that.userinfo.scorebdkyfyu); //-余额抵扣
      } else {
        var scorebdkyfyu = 0;
      }
      var oldalltotalprice = alltotalprice;
      alltotalprice = alltotalprice - scoredk_money;
      // alltotalprice = alltotalprice - scorebdkyfhei;
      if (alltotalprice < 0) alltotalprice = 0;
      if (that.scorebdkyf == '1' && scoredk_money > 0 && alltotalprice < allfreight_price) {
        //积分不抵扣运费
        alltotalprice = allfreight_price;
        scoredk_money = oldalltotalprice - allfreight_price;
      }
      if (that.scorebdkyfhei == '1' && scorebdkyfhei > 0 && alltotalprice < allfreight_price) {
        //黑积分不抵扣运费
        alltotalprice = allfreight_price;
        scorebdkyfhei = oldalltotalprice - allfreight_price;
      }
      if (that.scorebdkyfhuang == '1' && scorebdkyfhuang > 0 && alltotalprice < allfreight_price) {
        //黄积分不抵扣运费
        alltotalprice = allfreight_price;
        scorebdkyfhuang = oldalltotalprice - allfreight_price;
      }
      if (that.scorebdkyfyu == '1' && scorebdkyfyu > 0 && alltotalprice < allfreight_price) {
        //黑积分不抵扣运费
        alltotalprice = allfreight_price;
        scorebdkyfyu = oldalltotalprice - allfreight_price;
      }
      var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例
      var scoremaxtype = parseInt(that.userinfo.scoremaxtype);
      var scoredkmaxmoney = parseFloat(that.userinfo.scoredkmaxmoney);
      var scoredkmaxpercenthei = parseFloat(that.userinfo.scoredkmaxpercenthei); //最大抵扣比例
      var scoremaxtypehei = parseInt(that.userinfo.scoremaxtypehei);
      var scoredkmaxmoneyhei = parseFloat(that.userinfo.scoredkmaxmoneyhei);
      var scoredkmaxpercenthuang = parseFloat(that.userinfo.scoredkmaxpercenthuang); //最大抵扣比例
      var scoremaxtypehuang = parseInt(that.userinfo.scoremaxtypehuang);
      var scoredkmaxmoneyhuang = parseFloat(that.userinfo.scoredkmaxmoneyhuang);
      var scoredkmaxpercentyu = parseFloat(that.userinfo.scoredkmaxpercentyu); //最大抵扣比例
      var scoremaxtypeyu = parseInt(that.userinfo.scoremaxtypeyu);
      var scoredkmaxmoneyyu = parseFloat(that.userinfo.scoredkmaxmoneyyu);
      if (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > oldalltotalprice * scoredkmaxpercent * 0.01) {
        scoredk_money = oldalltotalprice * scoredkmaxpercent * 0.01;
        alltotalprice = oldalltotalprice - scoredk_money;
        // oldalltotalprice = oldalltotalprice - scoredk_money;
      } else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {
        scoredk_money = scoredkmaxmoney;
        alltotalprice = oldalltotalprice - scoredk_money;
        // oldalltotalprice = oldalltotalprice - scoredk_money;
      }

      if (scoremaxtypehei == 0 && scorebdkyfhei > 0 && scoredkmaxpercenthei > 0 && scoredkmaxpercenthei < 100 && scorebdkyfhei > oldalltotalprice * scoredkmaxpercenthei * 0.01) {
        scorebdkyfhei = alltotalprice * scoredkmaxpercenthei * 0.01;
        alltotalprice = alltotalprice - scorebdkyfhei;
        // oldalltotalprice = oldalltotalprice - scorebdkyfhei;
      } else if (scoremaxtypehei == 1 && scorebdkyfhei > scoredkmaxmoneyhei) {
        scorebdkyfhei = scoredkmaxmoneyhei;
        alltotalprice = alltotalprice - scorebdkyfhei;
        // oldalltotalprice = oldalltotalprice - scorebdkyfhei;
      }

      //黄积分
      if (scoremaxtypehuang == 0 && scorebdkyfhuang > 0 && scoredkmaxpercenthuang > 0 && scoredkmaxpercenthuang < 100 && scorebdkyfhuang > oldalltotalprice * scoredkmaxpercenthuang * 0.01) {
        scorebdkyfhuang = alltotalprice * scoredkmaxpercenthuang * 0.01;
        alltotalprice = alltotalprice - scorebdkyfhuang;
        // oldalltotalprice = oldalltotalprice - scorebdkyfhuang;
      } else if (scoremaxtypehuang == 1 && scorebdkyfhuang > scoredkmaxmoneyhuang) {
        scorebdkyfhuang = scoredkmaxmoneyhuang;
        alltotalprice = alltotalprice - scorebdkyfhuang;
        // oldalltotalprice = oldalltotalprice - scorebdkyfhuang;
      }

      if (scoremaxtypeyu == 0 && scorebdkyfyu > 0 && scoredkmaxpercentyu > 0 && scoredkmaxpercentyu < 100 && scorebdkyfyu > oldalltotalprice * scoredkmaxpercentyu * 0.01) {
        scorebdkyfyu = alltotalprice * scoredkmaxpercentyu * 0.01;
        alltotalprice = alltotalprice - scorebdkyfyu;
      } else if (scoremaxtypeyu == 1 && scorebdkyfyu > scoredkmaxmoneyyu) {
        scorebdkyfyu = scoredkmaxmoneyyu;
        alltotalprice = alltotalprice - scorebdkyfyu;
      }
      if (alltotalprice < 0) alltotalprice = 0;
      alltotalprice = alltotalprice.toFixed(2);
      that.alltotalprice = alltotalprice;
      that.allbuydata = allbuydata;
    },
    changeFreight: function changeFreight(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var index = e.currentTarget.dataset.index;
      var freightList = allbuydata[bid].freightList;
      if (freightList[index].pstype == 1 && freightList[index].storedata.length < 1) {
        app.error('无可自提门店');
        return;
      }
      if (freightList[index].pstype == 5 && freightList[index].storedata.length < 1) {
        app.error('无可配送门店');
        return;
      }
      allbuydata[bid].freightkey = index;
      that.allbuydata = allbuydata;
      that.calculatePrice();
      that.allbuydata[bid].editorFormdata = [];
    },
    chooseFreight: function chooseFreight(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      // console.log(bid);
      // console.log(allbuydata);
      var freightList = allbuydata[bid].freightList;
      var itemlist = [];
      for (var i = 0; i < freightList.length; i++) {
        itemlist.push(freightList[i].name);
      }
      uni.showActionSheet({
        itemList: itemlist,
        success: function success(res) {
          if (res.tapIndex >= 0) {
            allbuydata[bid].freightkey = res.tapIndex;
            that.allbuydata = allbuydata;
            that.calculatePrice();
          }
        }
      });
    },
    choosePstime: function choosePstime(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var freightkey = allbuydata[bid].freightkey;
      var freightList = allbuydata[bid].freightList;
      var freight = freightList[freightkey];
      var pstimeArr = freightList[freightkey].pstimeArr;
      var itemlist = [];
      for (var i = 0; i < pstimeArr.length; i++) {
        itemlist.push(pstimeArr[i].title);
      }
      if (itemlist.length == 0) {
        app.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');
        return;
      }
      that.nowbid = bid;
      that.pstimeDialogShow = true;
      that.pstimeIndex = -1;
    },
    pstimeRadioChange: function pstimeRadioChange(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var pstimeIndex = e.currentTarget.dataset.index;
      // console.log(pstimeIndex)
      var nowbid = that.nowbid;
      var freightkey = allbuydata[nowbid].freightkey;
      var freightList = allbuydata[nowbid].freightList;
      var freight = freightList[freightkey];
      var pstimeArr = freightList[freightkey].pstimeArr;
      var choosepstime = pstimeArr[pstimeIndex];
      allbuydata[nowbid].pstimetext = choosepstime.title;
      allbuydata[nowbid].freight_time = choosepstime.value;
      that.allbuydata = allbuydata;
      that.pstimeDialogShow = false;
    },
    hidePstimeDialog: function hidePstimeDialog() {
      this.pstimeDialogShow = false;
    },
    chooseCoupon: function chooseCoupon(e) {
      var allbuydata = this.allbuydata;
      var bid = e.bid;
      var couponrid = e.rid;
      var couponkey = e.key;
      var oldcoupons = allbuydata[bid].coupons;
      var oldcouponrids = allbuydata[bid].couponrids;
      var couponList = allbuydata[bid].couponList;
      if (app.inArray(couponrid, oldcouponrids)) {
        var coupons = [];
        var couponrids = [];
        for (var i in oldcoupons) {
          if (oldcoupons[i].id != couponrid) {
            coupons.push(oldcoupons[i]);
            couponrids.push(oldcoupons[i].id);
          }
        }
      } else {
        coupons = oldcoupons;
        couponrids = oldcouponrids;
        console.log(allbuydata[bid].coupon_peruselimit + '---' + oldcouponrids.length);
        if (allbuydata[bid].coupon_peruselimit > oldcouponrids.length) {
          console.log('xxxx');
          coupons.push(couponList[couponkey]);
          couponrids.push(couponList[couponkey].id);
        } else {
          if (allbuydata[bid].coupon_peruselimit > 1) {
            app.error('最多只能选用' + allbuydata[bid].coupon_peruselimit + '张');
            return;
          } else {
            coupons = [couponList[couponkey]];
            couponrids = [couponrid];
          }
        }
      }
      console.log(coupons);
      console.log(couponrids);
      allbuydata[bid].coupons = coupons;
      allbuydata[bid].couponrids = couponrids;
      var coupon_money = 0;
      var coupontype = 1;
      for (var i in coupons) {
        if (coupons[i]['type'] == 4) {
          coupontype = 4;
        } else if (coupons[i]['type'] == 10) {
          coupon_money += coupons[i]['thistotalprice'] * (100 - coupons[i]['discount']) * 0.01;
        } else {
          coupon_money += coupons[i]['money'];
        }
      }
      allbuydata[bid].coupontype = coupontype;
      allbuydata[bid].coupon_money = coupon_money;
      this.allbuydata = allbuydata;
      this.couponvisible = false;
      this.calculatePrice();
    },
    choosestore: function choosestore(e) {
      var bid = e.currentTarget.dataset.bid;
      var storekey = e.currentTarget.dataset.index;
      var allbuydata = this.allbuydata;
      var buydata = allbuydata[bid];
      var freightkey = buydata.freightkey;
      allbuydata[bid].freightList[freightkey].storekey = storekey;
      this.allbuydata = allbuydata;
    },
    //提交并支付
    topay: function topay(e) {
      var that = this;
      var needaddress = that.needaddress;
      var addressid = this.address.id;
      var checkmemid = this.checkMem.id;
      var linkman = this.linkman;
      var tel = this.tel;
      var usescore = this.usescore;
      var usescorehei = this.usescorehei;
      var usescoreyu = this.usescoreyu;
      var usescorehuang = this.usescorehuang;
      var frompage = that.opt.frompage ? that.opt.frompage : '';
      var allbuydata = that.allbuydata;
      var is_yh = that.is_yh;
      var yh_prices = that.yh_prices;
      var yh_nums = that.yh_nums;
      var newarr = that.newArr;
      if (needaddress == 0) addressid = 0;
      if (needaddress == 1 && addressid == undefined) {
        app.error('请选择收货地址');
        return;
      }
      var buydata = [];
      for (var i in allbuydata) {
        var freightkey = allbuydata[i].freightkey;
        if (allbuydata[i].freightList[freightkey].pstimeset == 1 && allbuydata[i].freight_time == '') {
          app.error('请选择' + (allbuydata[i].freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间');
          return;
        }
        if (allbuydata[i].freightList[freightkey].pstype == 1 || allbuydata[i].freightList[freightkey].pstype == 5) {
          var storekey = allbuydata[i].freightList[freightkey].storekey;
          var storeid = allbuydata[i].freightList[freightkey].storedata[storekey].id;
        } else {
          var storeid = 0;
        }
        if (allbuydata[i].freightList[freightkey].pstype == 11) {
          var type11key = allbuydata[i].type11key;
          if (type11key == 0 || !type11key) {
            app.error('请选择物流');
            return;
          }
          type11key = type11key - 1;
        } else {
          var type11key = 0;
        }
        var formdata_fields = allbuydata[i].freightList[freightkey].formdata;
        var formdata = e.detail.value;
        var newformdata = {};
        for (var j = 0; j < formdata_fields.length; j++) {
          var thisfield = 'form' + allbuydata[i].bid + '_' + j;
          if (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length == 0)) {
            app.alert(formdata_fields[j].val1 + ' 必填');
            return;
          }
          if (formdata_fields[j].key == 'selector') {
            formdata[thisfield] = formdata_fields[j].val2[formdata[thisfield]];
          }
          if (j > 0 && formdata_fields[j].val1 == '确认账号' && formdata_fields[j - 1].val1 == '充值账号' && formdata[thisfield] != formdata['form' + allbuydata[i].bid + '_' + (j - 1)]) {
            app.alert('两次输入账号不一致');
            return;
          }
          newformdata['form' + j] = formdata[thisfield];
        }
        var couponrid = allbuydata[i].couponrids.join(',');
        var buydatatemp = {
          bid: allbuydata[i].bid,
          prodata: allbuydata[i].prodatastr,
          cuxiaoid: allbuydata[i].cuxiaoid,
          couponrid: couponrid,
          freight_id: allbuydata[i].freightList[freightkey].id,
          freight_time: allbuydata[i].freight_time,
          storeid: storeid,
          formdata: newformdata,
          type11key: type11key
        };
        if (that.order_change_price) {
          buydatatemp.prodataList = allbuydata[i].prodata;
        }
        if (allbuydata[i].business.invoice) {
          buydatatemp.invoice = allbuydata[i].tempInvoice;
        }
        buydata.push(buydatatemp);
      }
      // console.log(buydata);return;
      app.showLoading('提交中');
      app.post('ApiShop/createOrder', {
        frompage: frompage,
        buydata: buydata,
        addressid: addressid,
        linkman: linkman,
        tel: tel,
        checkmemid: checkmemid,
        usescore: usescore,
        usescorehei: usescorehei,
        usescoreyu: usescoreyu,
        usescorehuang: usescorehuang,
        is_yh: is_yh,
        yh_prices: yh_prices,
        yh_nums: yh_nums,
        newarr: newarr
      }, function (res) {
        app.showLoading(false);
        if (res.status == 0) {
          //that.showsuccess(res.data.msg);
          app.error(res.msg);
          return;
        }
        //app.error('订单编号：' +res.payorderid);
        if (res.payorderid) app.goto('/pages/pay/pay?id=' + res.payorderid);
      });
    },
    showCouponList: function showCouponList(e) {
      this.couponvisible = true;
      this.bid = e.currentTarget.dataset.bid;
    },
    showInvoice: function showInvoice(e) {
      this.invoiceShow = true;
      this.bid = e.currentTarget.dataset.bid;
      var index = e.currentTarget.dataset.index;
      this.invoice_type = this.allbuydata[index].business.invoice_type;
      this.invoice = this.allbuydata[index].tempInvoice;
    },
    changeOrderType: function changeOrderType(e) {
      var that = this;
      var value = e.detail.value;
      if (value == 2) {
        that.name_type_select = 2;
        that.name_type_personal_disabled = true;
      } else {
        that.name_type_personal_disabled = false;
      }
      that.invoice_type_select = value;
    },
    changeNameType: function changeNameType(e) {
      var that = this;
      var value = e.detail.value;
      that.name_type_select = value;
    },
    invoiceFormSubmit: function invoiceFormSubmit(e) {
      var that = this;
      var formdata = e.detail.value;
      if (formdata.invoice_name == '') {
        app.error('请填写抬头名称');
        return;
      }
      if ((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {
        ///^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/
        app.error('请填写公司税号');
        return;
      }
      if (formdata.invoice_type == 2) {
        if (formdata.address == '') {
          app.error('请填写注册地址');
          return;
        }
        if (formdata.tel == '') {
          app.error('请填写注册电话');
          return;
        }
        if (formdata.bank_name == '') {
          app.error('请填写开户银行');
          return;
        }
        if (formdata.bank_account == '') {
          app.error('请填写银行账号');
          return;
        }
      }
      if (formdata.mobile != '') {
        if (!/^1[3456789]\d{9}$/.test(formdata.mobile)) {
          app.error("手机号码有误，请重填");
          return;
        }
      }
      if (formdata.email != '') {
        if (!/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/.test(formdata.email)) {
          app.error("邮箱有误，请重填");
          return;
        }
      }
      if (formdata.mobile == '' && formdata.email == '') {
        app.error("手机号和邮箱请填写其中一个");
        return;
      }
      // console.log(formdata);
      var allbuydata = that.allbuydata;
      for (var i in allbuydata) {
        if (allbuydata[i].bid == that.bid) allbuydata[i].tempInvoice = formdata;
      }
      that.allbuydata = allbuydata;
      that.invoiceShow = false;
      // that.loading = true;
      // uni.setStorageSync('temp_invoice_' + that.opt.bid, formdata);
      that.calculatePrice();
    },
    handleClickMask: function handleClickMask() {
      this.couponvisible = false;
      this.cuxiaovisible = false;
      this.type11visible = false;
      this.membervisible = false;
      this.invoiceShow = false;
    },
    showCuxiaoList: function showCuxiaoList(e) {
      this.cuxiaovisible = true;
      this.bid = e.currentTarget.dataset.bid;
    },
    changecx: function changecx(e) {
      var that = this;
      var cxid = e.currentTarget.dataset.id;
      var cxindex = e.currentTarget.dataset.index;
      console.log(cxid);
      that.cxid = cxid;
      if (cxid == 0) {
        that.cuxiaoinfo = false;
        return;
      }
      var cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];
      console.log(cuxiaoinfo.cuxiaomoney);
      app.post("ApiShop/getcuxiaoinfo", {
        id: cxid
      }, function (res) {
        if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {
          res.cuxiaomoney = cuxiaoinfo.cuxiaomoney;
        }
        // 添加赠送信息
        if (cuxiaoinfo.give_jifen > 0) {
          res.give_jifen = cuxiaoinfo.give_jifen;
        }
        if (cuxiaoinfo.give_money > 0) {
          res.give_money = cuxiaoinfo.give_money;
        }
        if (cuxiaoinfo.give_cash_coupon > 0) {
          res.give_cash_coupon = cuxiaoinfo.give_cash_coupon;
        }
        if (cuxiaoinfo.give_yellow_points > 0) {
          res.give_yellow_points = cuxiaoinfo.give_yellow_points;
        }
        that.cuxiaoinfo = res;
      });
    },
    changecxMulti: function changecxMulti(e) {
      var that = this;
      var cxid = e.currentTarget.dataset.id;
      var cxindex = e.currentTarget.dataset.index;
      that.cuxiaoList.length = 0;
      console.log('cxid:' + cxid);
      if (cxid == 0) {
        that.cuxiaoinfo = false;
        that.cxids.length = 0;
        that.cxid = 0;
        return;
      }
      var index = that.cxids.indexOf(cxid);
      if (index === -1) {
        that.cxids.push(cxid);
      } else {
        that.cxids.splice(index);
      }
      if (that.cxids.length == 0) {
        that.cxid = 0;
        that.cuxiaoinfo = false;
        return;
      }
      that.cxid = '';
      var cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];
      console.log(cuxiaoinfo.cuxiaomoney);
      app.showLoading();
      app.post("ApiShop/getcuxiaoinfo", {
        id: that.cxids
      }, function (res) {
        // if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {
        // 	res.cuxiaomoney = cuxiaoinfo.cuxiaomoney
        // }
        // 添加赠送信息
        if (res.info && res.info.length > 0) {
          for (var i = 0; i < res.info.length; i++) {
            var item = that.allbuydata[that.bid].cuxiaolist.find(function (c) {
              return c.id == res.info[i].id;
            });
            if (item) {
              if (item.give_jifen > 0) {
                res.info[i].give_jifen = item.give_jifen;
              }
              if (item.give_money > 0) {
                res.info[i].give_money = item.give_money;
              }
              if (item.give_cash_coupon > 0) {
                res.info[i].give_cash_coupon = item.give_cash_coupon;
              }
              if (item.give_yellow_points > 0) {
                res.info[i].give_yellow_points = item.give_yellow_points;
              }
            }
          }
        }
        app.showLoading(false);
        that.cuxiaoList = res;
      });
    },
    chooseCuxiao: function chooseCuxiao() {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = that.bid;
      var cxid = that.cxid;
      var cxids = that.cxids;
      console.log(cxid == 0);
      if (cxid == 0 || cxid == '') {
        allbuydata[bid].cuxiaoid = '';
        allbuydata[bid].cuxiao_money = 0;
        allbuydata[bid].cuxiaoname = '不使用促销';
        allbuydata[bid].cuxiaonameArr = [];
      } else {
        allbuydata[bid].cuxiaoid = [];
        allbuydata[bid].cuxiao_money = 0;
        allbuydata[bid].cuxiaotype = [];
        allbuydata[bid].cuxiaonameArr = [];
        console.log(that.cuxiaoList.info);
        if (that.cuxiaoList.info && that.cuxiaoList.info.length > 0) {
          for (var i in that.cuxiaoList.info) {
            var cxtype = that.cuxiaoList.info[i].type;
            console.log(cxtype);
            if (cxtype == 1 || cxtype == 6) {
              //满额立减 满件立减
              allbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'] * -1;
            } else if (cxtype == 2) {
              //满额赠送
              allbuydata[bid].cuxiao_money += 0;
            } else if (cxtype == 3) {
              //加价换购  27.8+15.964+41.4
              allbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'];
            } else if (cxtype == 4 || cxtype == 5) {
              //满额打折 满件打折
              var cuxiaoMoney = 0;
              for (var y in that.allbuydata[bid].cuxiaolist) {
                if (that.cuxiaoList.info[i].id == that.allbuydata[bid].cuxiaolist[y].id) {
                  cuxiaoMoney = that.allbuydata[bid].cuxiaolist[y].cuxiaomoney;
                }
              }
              console.log('cuxiaoMoney');
              console.log(cuxiaoMoney);
              allbuydata[bid].cuxiao_money += cuxiaoMoney * -1;
            }
            allbuydata[bid].cuxiaoid.push(that.cuxiaoList.info[i].id);
            allbuydata[bid].cuxiaotype.push(cxtype);
            allbuydata[bid].cuxiaonameArr.push(that.cuxiaoList.info[i]['name']);
          }
          console.log('allbuydata[bid]');
          console.log(allbuydata[bid]);
        } else {
          var cxtype = that.cuxiaoinfo.info.type;
          console.log(cxtype);
          if (cxtype == 1 || cxtype == 6) {
            //满额立减 满件立减
            allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'] * -1;
          } else if (cxtype == 2) {
            //满额赠送
            allbuydata[bid].cuxiao_money = 0;
          } else if (cxtype == 3) {
            //加价换购  27.8+15.964+41.4
            allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'];
          } else if (cxtype == 4 || cxtype == 5) {
            //var product_price = parseFloat(allbuydata[bid].product_price);
            //var leveldk_money = parseFloat(allbuydata[bid].leveldk_money); //会员折扣
            //var manjian_money = parseFloat(allbuydata[bid].manjian_money); //满减活动
            //满额打折 满件打折
            //allbuydata[bid].cuxiao_money = (1 - that.cuxiaoinfo.info['zhekou'] * 0.1) * (product_price - leveldk_money - manjian_money) * -1;
            allbuydata[bid].cuxiao_money = that.cuxiaoinfo.cuxiaomoney * -1;
          }
          allbuydata[bid].cuxiaoid = cxid;
          allbuydata[bid].cuxiaotype = cxtype;
          allbuydata[bid].cuxiaoname = that.cuxiaoinfo.info['name'];
        }
      }
      this.allbuydata = allbuydata;
      this.cuxiaovisible = false;
      this.calculatePrice();
    },
    showType11List: function showType11List(e) {
      this.type11visible = true;
      this.bid = e.currentTarget.dataset.bid;
    },
    changetype11: function changetype11(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = that.bid;
      that.type11key = e.currentTarget.dataset.index;
      // console.log(that.type11key)
    },

    chooseType11: function chooseType11(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = that.bid;
      var type11key = that.type11key;
      if (type11key == -1) {
        app.error('请选择物流');
        return;
      }
      allbuydata[bid].type11key = type11key + 1;
      // console.log(allbuydata[bid].type11key)
      var freightkey = allbuydata[bid].freightkey;
      var freightList = allbuydata[bid].freightList;
      var freight_price = parseFloat(freightList[freightkey].type11pricedata[type11key].price);
      var product_price = parseFloat(allbuydata[bid].product_price);
      // console.log(freightList[freightkey].freeset);
      // console.log(parseFloat(freightList[freightkey].free_price));
      // console.log(product_price);
      if (freightList[freightkey].freeset == 1 && parseFloat(freightList[freightkey].free_price) <= product_price) {
        freight_price = 0;
      }
      allbuydata[bid].freightList[freightkey].freight_price = freight_price;
      this.allbuydata = allbuydata;
      this.type11visible = false;
      this.calculatePrice();
    },
    openMendian: function openMendian(e) {
      var allbuydata = this.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var freightkey = e.currentTarget.dataset.freightkey;
      var storekey = e.currentTarget.dataset.storekey;
      var frightinfo = allbuydata[bid].freightList[freightkey];
      var storeinfo = frightinfo.storedata[storekey];
      // console.log(storeinfo)
      app.goto('mendian?id=' + storeinfo.id);
    },
    openLocation: function openLocation(e) {
      var allbuydata = this.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var freightkey = e.currentTarget.dataset.freightkey;
      var storekey = e.currentTarget.dataset.storekey;
      var frightinfo = allbuydata[bid].freightList[freightkey];
      var storeinfo = frightinfo.storedata[storekey];
      // console.log(storeinfo)
      var latitude = parseFloat(storeinfo.latitude);
      var longitude = parseFloat(storeinfo.longitude);
      var address = storeinfo.name;
      uni.openLocation({
        latitude: latitude,
        longitude: longitude,
        name: address,
        scale: 13
      });
    },
    editorChooseImage: function editorChooseImage(e) {
      var that = this;
      var bid = e.currentTarget.dataset.bid;
      var idx = e.currentTarget.dataset.idx;
      var editorFormdata = that.allbuydata[bid].editorFormdata;
      if (!editorFormdata) editorFormdata = [];
      app.chooseImage(function (data) {
        editorFormdata[idx] = data[0];
        // console.log(editorFormdata)
        that.allbuydata[bid].editorFormdata = editorFormdata;
        that.test = Math.random();
      });
    },
    editorBindPickerChange: function editorBindPickerChange(e) {
      var that = this;
      var bid = e.currentTarget.dataset.bid;
      var idx = e.currentTarget.dataset.idx;
      var val = e.detail.value;
      var editorFormdata = that.allbuydata[bid].editorFormdata;
      if (!editorFormdata) editorFormdata = [];
      editorFormdata[idx] = val;
      // console.log(editorFormdata)
      that.allbuydata[bid].editorFormdata = editorFormdata;
      that.test = Math.random();
    },
    showMemberList: function showMemberList(e) {
      this.membervisible = true;
    },
    regionchange2: function regionchange2(e) {
      var value = e.detail.value;
      // console.log(value[0].text + ',' + value[1].text + ',' + value[2].text);
      this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;
    },
    memberSearch: function memberSearch() {
      var that = this;
      // console.log(that.regiondata)
      app.post('ApiShop/memberSearch', {
        diqu: that.regiondata
      }, function (res) {
        app.showLoading(false);
        if (res.status == 0) {
          app.error(res.msg);
          return;
        }
        var data = res.memberList;
        that.memberList = data;
      });
    },
    checkMember: function checkMember(e) {
      var that = this;
      that.checkMem = e.currentTarget.dataset.info;
      this.membervisible = false;
    },
    showmemberinfo: function showmemberinfo(e) {
      var that = this;
      var mid = e.currentTarget.dataset.mid;
      app.showLoading('提交中');
      app.post('ApiShop/getmemberuplvinfo', {
        mid: mid
      }, function (res) {
        app.showLoading(false);
        if (res.status == 0) {
          app.error(res.msg);
          return;
        }
        that.selectmemberinfo = res.info;
        that.memberinfovisible = true;
      });
    },
    memberinfoClickMask: function memberinfoClickMask() {
      this.memberinfovisible = false;
    },
    doStoreShowAll: function doStoreShowAll() {
      this.storeshowall = true;
    },
    showglass: function showglass(e) {
      var that = this;
      var grid = e.currentTarget.dataset.grid;
      var index = e.currentTarget.dataset.index;
      var index2 = e.currentTarget.dataset.index2;
      console.log(grid);
      console.log(index);
      console.log(index2);
      // console.log(that.glassrecordlist)
      if (that.glassrecordlist.length < 1) {
        //没有数据 就重新请求
        that.loading;
        app.post('ApiGlass/myrecord', {
          pagenum: 1,
          listrow: 100
        }, function (res) {
          that.loading = false;
          var datalist = res.data;
          console.log(datalist);
          that.glassrecordlist = datalist;
          // console.log(that.glassrecordlist);
          // if(datalist.length>0){
          // 	// 
          // 	that.isshowglass = true
          // }else{
          // 	app.error('无可用的视力档案')
          // }
          that.isshowglass = true;
        });
      } else {
        that.isshowglass = true;
      }
      that.curindex = index;
      that.curindex2 = index2;
      that.grid = grid;
    },
    hideglass: function hideglass(e) {
      var that = this;
      that.isshowglass = false;
    },
    chooseglass: function chooseglass(e) {
      var that = this;
      var gindex = e.detail.value;
      var allbuydata = that.allbuydata;
      var grid = that.grid;
      var index = that.curindex;
      var index2 = that.curindex2;
      console.log(gindex + '-' + that.curindex + '-' + that.curindex2);
      var glassrecordlist = that.glassrecordlist;
      var product = allbuydata[index]['prodata'][index2].product;
      var sid = glassrecordlist[gindex].id;
      if (grid == sid) {
        product.glassrecord = {};
        that.grid = 0;
      } else {
        product.glassrecord = glassrecordlist[gindex];
        that.grid = glassrecordlist[gindex].id;
      }
      that.allbuydata[index]['prodata'][index2]['product'] = product;
      that.isshowglass = false;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 195:
/*!*******************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?vue&type=style&index=0&lang=css& ***!
  \*******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css& */ 196);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 196:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?vue&type=style&index=0&lang=css& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[189,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/shopPackage/shop/buy.js.map