(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["shopPackage/shop/product"],{

/***/ 146:
/*!*********************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/main.js?{"page":"shopPackage%2Fshop%2Fproduct"} ***!
  \*********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _product = _interopRequireDefault(__webpack_require__(/*! ./shopPackage/shop/product.vue */ 147));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_product.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 147:
/*!**************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./product.vue?vue&type=template&id=6bf535ba& */ 148);
/* harmony import */ var _product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./product.vue?vue&type=script&lang=js& */ 150);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _product_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./product.vue?vue&type=style&index=0&lang=css& */ 155);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 34);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__["render"],
  _product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "shopPackage/shop/product.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 148:
/*!*********************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?vue&type=template&id=6bf535ba& ***!
  \*********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=6bf535ba& */ 149);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_template_id_6bf535ba___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 149:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?vue&type=template&id=6bf535ba& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return Promise.all(/*! import() | components/uni-popup/uni-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-popup/uni-popup")]).then(__webpack_require__.bind(null, /*! @/components/uni-popup/uni-popup.vue */ 7841))
    },
    couponlist: function () {
      return __webpack_require__.e(/*! import() | components/couponlist/couponlist */ "components/couponlist/couponlist").then(__webpack_require__.bind(null, /*! @/components/couponlist/couponlist.vue */ 7922))
    },
    dp: function () {
      return __webpack_require__.e(/*! import() | components/dp/dp */ "components/dp/dp").then(__webpack_require__.bind(null, /*! @/components/dp/dp.vue */ 7850))
    },
    dpProductItem: function () {
      return __webpack_require__.e(/*! import() | components/dp-product-item/dp-product-item */ "components/dp-product-item/dp-product-item").then(__webpack_require__.bind(null, /*! @/components/dp-product-item/dp-product-item.vue */ 7929))
    },
    buydialog: function () {
      return __webpack_require__.e(/*! import() | components/buydialog/buydialog */ "components/buydialog/buydialog").then(__webpack_require__.bind(null, /*! @/components/buydialog/buydialog.vue */ 7936))
    },
    loading: function () {
      return __webpack_require__.e(/*! import() | components/loading/loading */ "components/loading/loading").then(__webpack_require__.bind(null, /*! @/components/loading/loading.vue */ 5109))
    },
    dpTabbar: function () {
      return __webpack_require__.e(/*! import() | components/dp-tabbar/dp-tabbar */ "components/dp-tabbar/dp-tabbar").then(__webpack_require__.bind(null, /*! @/components/dp-tabbar/dp-tabbar.vue */ 5116))
    },
    popmsg: function () {
      return __webpack_require__.e(/*! import() | components/popmsg/popmsg */ "components/popmsg/popmsg").then(__webpack_require__.bind(null, /*! @/components/popmsg/popmsg.vue */ 5123))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t("color1") : null
  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t("color1") : null
  var g0 = _vm.isload ? _vm.bboglist.length : null
  var m2 =
    _vm.isload &&
    _vm.showtoptabbar == 1 &&
    _vm.toptabbar_show == 1 &&
    _vm.toptabbar_index == 0
      ? _vm.t("color1")
      : null
  var m3 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1
      ? _vm.t("color1")
      : null
  var m4 =
    _vm.isload &&
    _vm.showtoptabbar == 1 &&
    _vm.toptabbar_show == 1 &&
    _vm.toptabbar_index == 1
      ? _vm.t("color1")
      : null
  var m5 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1
      ? _vm.t("color1")
      : null
  var m6 =
    _vm.isload &&
    _vm.showtoptabbar == 1 &&
    _vm.toptabbar_show == 1 &&
    _vm.toptabbar_index == 2
      ? _vm.t("color1")
      : null
  var m7 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1
      ? _vm.t("color1")
      : null
  var g1 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1
      ? _vm.tjdatalist.length
      : null
  var m8 =
    _vm.isload &&
    _vm.showtoptabbar == 1 &&
    _vm.toptabbar_show == 1 &&
    g1 > 0 &&
    _vm.toptabbar_index == 3
      ? _vm.t("color1")
      : null
  var m9 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1 && g1 > 0
      ? _vm.t("color1")
      : null
  var g2 = _vm.isload && _vm.isplay == 0 ? _vm.product.pics.length : null
  var g3 = _vm.isload
    ? _vm.showtoptabbar == 1 && _vm.couponlist.length > 0
    : null
  var l0 =
    _vm.isload && g3
      ? _vm.__map(_vm.couponlist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m10 = _vm.t("color1rgb")
          var m11 = _vm.t("color1")
          return {
            $orig: $orig,
            m10: m10,
            m11: m11,
          }
        })
      : null
  var m12 = _vm.isload && _vm.product.hide_price == 1 ? _vm.t("color1") : null
  var m13 =
    _vm.isload &&
    !(_vm.product.hide_price == 1) &&
    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&
    _vm.product.is_newcustom == 1 &&
    _vm.product.is_member_yh == 0
      ? _vm.t("color1")
      : null
  var m14 =
    _vm.isload &&
    !(_vm.product.hide_price == 1) &&
    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&
    !(_vm.product.is_newcustom == 1 && _vm.product.is_member_yh == 0)
      ? _vm.t("color1")
      : null
  var m15 =
    _vm.isload &&
    !(_vm.product.hide_price == 1) &&
    !(_vm.product.price_type != 1 || _vm.product.min_price > 0) &&
    _vm.product.xunjia_text
      ? _vm.t("color1")
      : null
  var m16 =
    _vm.isload &&
    _vm.shopset.showcommission == 1 &&
    _vm.product.commission > 0 &&
    _vm.showjiesheng == 0
      ? _vm.t("color1rgb")
      : null
  var m17 =
    _vm.isload &&
    _vm.shopset.showcommission == 1 &&
    _vm.product.commission > 0 &&
    _vm.showjiesheng == 0
      ? _vm.t("color1")
      : null
  var m18 =
    _vm.isload &&
    _vm.shopset.showcommission == 1 &&
    _vm.product.commission > 0 &&
    _vm.showjiesheng == 0
      ? _vm.t("佣金")
      : null
  var m19 =
    _vm.isload && _vm.product.buyselect_commission > 0 ? _vm.t("佣金") : null
  var m20 = _vm.isload && _vm.product.xiaofeizhi2 > 0 ? _vm.t("消费值") : null
  var m21 = _vm.isload && _vm.product.xiaofeizhi2 > 0 ? _vm.t("消费值") : null
  var m22 = _vm.isload && _vm.product.chuangyezhi > 0 ? _vm.t("创业值") : null
  var m23 = _vm.isload && _vm.product.chuangyezhi > 0 ? _vm.t("创业值") : null
  var m24 = _vm.isload && _vm.product.givescore > 0 ? _vm.t("积分") : null
  var m25 = _vm.isload && _vm.product.givescore > 0 ? _vm.t("积分") : null
  var m26 =
    _vm.isload && _vm.product.score_deduction_message ? _vm.t("积分") : null
  var g4 = _vm.isload
    ? _vm.cuxiaolist.length > 0 ||
      _vm.couponlist.length > 0 ||
      _vm.fuwulist.length > 0 ||
      _vm.product.discount_tips != ""
    : null
  var g5 = _vm.isload && g4 ? _vm.fuwulist.length : null
  var g6 = _vm.isload && g4 ? _vm.cuxiaolist.length : null
  var l1 =
    _vm.isload && g4 && g6 > 0
      ? _vm.__map(_vm.cuxiaolist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m27 = _vm.t("color1rgb")
          var m28 = _vm.t("color1")
          return {
            $orig: $orig,
            m27: m27,
            m28: m28,
          }
        })
      : null
  var g7 =
    _vm.isload && g4
      ? _vm.couponlist.length > 0 && _vm.showtoptabbar == 0
      : null
  var l2 =
    _vm.isload && g4 && g7
      ? _vm.__map(_vm.couponlist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m29 = _vm.t("color1rgb")
          var m30 = _vm.t("color1")
          return {
            $orig: $orig,
            m29: m29,
            m30: m30,
          }
        })
      : null
  var m31 =
    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0
      ? _vm.t("color1")
      : null
  var g8 =
    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0
      ? _vm.commentlist.length
      : null
  var g9 = _vm.isload ? _vm.notes && _vm.notes.length > 0 : null
  var l3 =
    _vm.isload && g9
      ? _vm.__map(_vm.notes, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var g10 = index % 3 === 0 ? item.pics && item.pics.length > 0 : null
          return {
            $orig: $orig,
            g10: g10,
          }
        })
      : null
  var l4 =
    _vm.isload && g9
      ? _vm.__map(_vm.notes, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var g11 = index % 3 === 1 ? item.pics && item.pics.length > 0 : null
          return {
            $orig: $orig,
            g11: g11,
          }
        })
      : null
  var l5 =
    _vm.isload && g9
      ? _vm.__map(_vm.notes, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var g12 = index % 3 === 2 ? item.pics && item.pics.length > 0 : null
          return {
            $orig: $orig,
            g12: g12,
          }
        })
      : null
  var m32 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t("color1") : null
  var m33 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t("color1rgb") : null
  var m34 = _vm.isload ? _vm.isEmpty(_vm.product.paramdata) : null
  var g13 = _vm.isload ? _vm.tjdatalist.length : null
  var l6 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    _vm.visibleMenuItemsCount > 0 &&
    _vm.shopdetail_menudataList
      ? _vm.__map(_vm.shopdetail_menudataList, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m35 =
            item.isShow == 1 && item.menuType == 2 && _vm.cartnum > 0
              ? _vm.t("color1rgb")
              : null
          return {
            $orig: $orig,
            m35: m35,
          }
        })
      : null
  var m36 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    _vm.visibleMenuItemsCount > 0 &&
    !_vm.shopdetail_menudataList &&
    _vm.cartnum > 0
      ? _vm.t("color1rgb")
      : null
  var m37 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    _vm.showjiesheng == 1
      ? _vm.t("color2")
      : null
  var m38 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    _vm.showjiesheng == 1 &&
    _vm.product.show_buybtn !== 0
      ? _vm.t("color1")
      : null
  var m39 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    !(_vm.showjiesheng == 1) &&
    _vm.product.hide_price == 1
      ? _vm.t("color1")
      : null
  var m40 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    !(_vm.showjiesheng == 1) &&
    !(_vm.product.hide_price == 1) &&
    _vm.product.price_type == 1
      ? _vm.t("color1")
      : null
  var m41 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    !(_vm.showjiesheng == 1) &&
    !(_vm.product.hide_price == 1) &&
    !(_vm.product.price_type == 1) &&
    _vm.product.show_addcartbtn !== 0 &&
    _vm.product.freighttype != 3 &&
    _vm.product.freighttype != 4
      ? _vm.t("color2")
      : null
  var m42 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    !(_vm.showjiesheng == 1) &&
    !(_vm.product.hide_price == 1) &&
    !(_vm.product.price_type == 1) &&
    _vm.product.show_buybtn !== 0
      ? _vm.t("color1")
      : null
  var m43 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null
  var m44 =
    _vm.isload && _vm.sharetypevisible && !(m43 == "app")
      ? _vm.getplatform()
      : null
  var m45 =
    _vm.isload && _vm.sharetypevisible && !(m43 == "app") && !(m44 == "mp")
      ? _vm.getplatform()
      : null
  var m46 =
    _vm.isload && _vm.sharetypevisible
      ? _vm.getplatform() == "wx" && _vm.xcx_scheme
      : null
  var m47 = _vm.isload && _vm.showposter ? _vm.t("color1") : null
  var m48 = _vm.isload && _vm.showposter ? _vm.t("color1rgb") : null
  var m49 = _vm.isload && _vm.showposter ? _vm.getplatform() : null
  var m50 =
    _vm.isload && _vm.showposter && m49 == "app" ? _vm.t("color1") : null
  var m51 =
    _vm.isload && _vm.showposter && m49 == "app" ? _vm.t("color1rgb") : null
  var m52 =
    _vm.isload && _vm.showposter && !(m49 == "app") ? _vm.getplatform() : null
  var m53 =
    _vm.isload && _vm.showposter && !(m49 == "app") && m52 == "mp"
      ? _vm.t("color1")
      : null
  var m54 =
    _vm.isload && _vm.showposter && !(m49 == "app") && m52 == "mp"
      ? _vm.t("color1rgb")
      : null
  var m55 =
    _vm.isload && _vm.showposter && !(m49 == "app") && !(m52 == "mp")
      ? _vm.getplatform()
      : null
  var m56 =
    _vm.isload &&
    _vm.showposter &&
    !(m49 == "app") &&
    !(m52 == "mp") &&
    m55 == "h5"
      ? _vm.t("color1")
      : null
  var m57 =
    _vm.isload &&
    _vm.showposter &&
    !(m49 == "app") &&
    !(m52 == "mp") &&
    m55 == "h5"
      ? _vm.t("color1rgb")
      : null
  var m58 =
    _vm.isload &&
    _vm.showposter &&
    !(m49 == "app") &&
    !(m52 == "mp") &&
    !(m55 == "h5")
      ? _vm.t("color1")
      : null
  var m59 =
    _vm.isload &&
    _vm.showposter &&
    !(m49 == "app") &&
    !(m52 == "mp") &&
    !(m55 == "h5")
      ? _vm.t("color1rgb")
      : null
  var m60 = _vm.isload && _vm.showScheme ? _vm.t("color1") : null
  var m61 = _vm.isload && _vm.showScheme ? _vm.t("color1rgb") : null
  var m62 =
    _vm.isload && _vm.showLinkStatus && _vm.business.tel
      ? _vm.t("color1")
      : null
  var m63 = _vm.getProbability()
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.product.addcart_link_url
        ? _vm.goToCustomLink("addcart")
        : _vm.buydialogChange(null, 1)
    }
    _vm.e1 = function ($event) {
      _vm.product.buybtn_link_url
        ? _vm.goToCustomLink("buybtn")
        : _vm.buydialogChange(null, 2)
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
        m6: m6,
        m7: m7,
        g1: g1,
        m8: m8,
        m9: m9,
        g2: g2,
        g3: g3,
        l0: l0,
        m12: m12,
        m13: m13,
        m14: m14,
        m15: m15,
        m16: m16,
        m17: m17,
        m18: m18,
        m19: m19,
        m20: m20,
        m21: m21,
        m22: m22,
        m23: m23,
        m24: m24,
        m25: m25,
        m26: m26,
        g4: g4,
        g5: g5,
        g6: g6,
        l1: l1,
        g7: g7,
        l2: l2,
        m31: m31,
        g8: g8,
        g9: g9,
        l3: l3,
        l4: l4,
        l5: l5,
        m32: m32,
        m33: m33,
        m34: m34,
        g13: g13,
        l6: l6,
        m36: m36,
        m37: m37,
        m38: m38,
        m39: m39,
        m40: m40,
        m41: m41,
        m42: m42,
        m43: m43,
        m44: m44,
        m45: m45,
        m46: m46,
        m47: m47,
        m48: m48,
        m49: m49,
        m50: m50,
        m51: m51,
        m52: m52,
        m53: m53,
        m54: m54,
        m55: m55,
        m56: m56,
        m57: m57,
        m58: m58,
        m59: m59,
        m60: m60,
        m61: m61,
        m62: m62,
        m63: m63,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 150:
/*!***************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js& */ 151);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 151:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 152));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 154));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var interval = null;
var _default = {
  data: function data() {
    var _ref;
    return _ref = {
      priceIndex: 0,
      luckyPriceArray: [],
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      textset: {}
    }, (0, _defineProperty2.default)(_ref, "isload", false), (0, _defineProperty2.default)(_ref, "buydialogShow", false), (0, _defineProperty2.default)(_ref, "btntype", 1), (0, _defineProperty2.default)(_ref, "isfavorite", false), (0, _defineProperty2.default)(_ref, "current", 0), (0, _defineProperty2.default)(_ref, "isplay", 0), (0, _defineProperty2.default)(_ref, "showcuxiaodialog", false), (0, _defineProperty2.default)(_ref, "showfuwudialog", false), (0, _defineProperty2.default)(_ref, "business", ""), (0, _defineProperty2.default)(_ref, "product", []), (0, _defineProperty2.default)(_ref, "cartnum", ""), (0, _defineProperty2.default)(_ref, "commentlist", ""), (0, _defineProperty2.default)(_ref, "commentcount", ""), (0, _defineProperty2.default)(_ref, "cuxiaolist", ""), (0, _defineProperty2.default)(_ref, "couponlist", ""), (0, _defineProperty2.default)(_ref, "fuwulist", []), (0, _defineProperty2.default)(_ref, "notes", []), (0, _defineProperty2.default)(_ref, "notes_total", 0), (0, _defineProperty2.default)(_ref, "pagecontent", ""), (0, _defineProperty2.default)(_ref, "shopset", {}), (0, _defineProperty2.default)(_ref, "sysset", {}), (0, _defineProperty2.default)(_ref, "title", ""), (0, _defineProperty2.default)(_ref, "bboglist", ""), (0, _defineProperty2.default)(_ref, "sharepic", ""), (0, _defineProperty2.default)(_ref, "sharetypevisible", false), (0, _defineProperty2.default)(_ref, "showposter", false), (0, _defineProperty2.default)(_ref, "posterpic", ""), (0, _defineProperty2.default)(_ref, "scrolltopshow", false), (0, _defineProperty2.default)(_ref, "kfurl", ''), (0, _defineProperty2.default)(_ref, "showLinkStatus", false), (0, _defineProperty2.default)(_ref, "showjiesheng", 0), (0, _defineProperty2.default)(_ref, "tjdatalist", []), (0, _defineProperty2.default)(_ref, "showtoptabbar", 0), (0, _defineProperty2.default)(_ref, "toptabbar_show", 0), (0, _defineProperty2.default)(_ref, "toptabbar_index", 0), (0, _defineProperty2.default)(_ref, "scrollToViewId", ""), (0, _defineProperty2.default)(_ref, "scrollTop", 0), (0, _defineProperty2.default)(_ref, "scrolltab0Height", 0), (0, _defineProperty2.default)(_ref, "scrolltab1Height", 0), (0, _defineProperty2.default)(_ref, "scrolltab2Height", 0), (0, _defineProperty2.default)(_ref, "scrolltab3Height", 0), (0, _defineProperty2.default)(_ref, "xcx_scheme", false), (0, _defineProperty2.default)(_ref, "showScheme", false), (0, _defineProperty2.default)(_ref, "schemeurl", ''), (0, _defineProperty2.default)(_ref, "isGameOpen", false), (0, _defineProperty2.default)(_ref, "gameList", []), (0, _defineProperty2.default)(_ref, "shopdetail_menudataList", []), (0, _defineProperty2.default)(_ref, "bottomImg", ''), (0, _defineProperty2.default)(_ref, "luckyPrice", 0.00), (0, _defineProperty2.default)(_ref, "currentGameInfo", {}), _ref;
  },
  computed: {
    // 判断是否有可见的菜单项
    hasVisibleMenuItems: function hasVisibleMenuItems() {
      if (!this.shopdetail_menudataList || !Array.isArray(this.shopdetail_menudataList)) {
        return false;
      }
      return this.shopdetail_menudataList.some(function (item) {
        return item.isShow == 1;
      });
    },
    // 计算可见菜单项的数量
    visibleMenuItemsCount: function visibleMenuItemsCount() {
      if (!this.shopdetail_menudataList || !Array.isArray(this.shopdetail_menudataList)) {
        return 3; // 默认3个菜单项（客服、购物车、收藏）
      }

      return this.shopdetail_menudataList.filter(function (item) {
        return item.isShow == 1;
      }).length;
    },
    // 动态计算f1容器的宽度
    f1ContainerWidth: function f1ContainerWidth() {
      var itemWidth = 90; // 每个菜单项宽度 100rpx (与CSS中.item的width一致)
      var margin = 20; // 右边距 20rpx (调整以平衡左右空间)
      return this.visibleMenuItemsCount * itemWidth + margin + 'rpx';
    }
  },
  onLoad: function onLoad(opt) {
    this.opt = app.getopts(opt);
    this.getdata();
    this.getSweepstakesStatus();
  },
  onShow: function onShow(e) {
    uni.$emit('getglassrecord');
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.getdata();
  },
  onShareAppMessage: function onShareAppMessage() {
    return this._sharewx({
      title: this.product.sharetitle || this.product.name,
      pic: this.product.sharepic || this.product.pic
    });
  },
  onShareTimeline: function onShareTimeline() {
    var sharewxdata = this._sharewx({
      title: this.product.sharetitle || this.product.name,
      pic: this.product.sharepic || this.product.pic
    });
    var query = sharewxdata.path.split('?')[1];
    console.log(sharewxdata);
    console.log(query);
    return {
      title: sharewxdata.title,
      imageUrl: sharewxdata.imageUrl,
      query: query
    };
  },
  onUnload: function onUnload() {
    clearInterval(interval);
    this.$refs.popupLucky && this.$refs.popupLucky.close();
  },
  methods: {
    /**
     * 获取抽奖总状态
     */
    getSweepstakesStatus: function getSweepstakesStatus() {
      var that = this;
      that.loading = true;
      var params = {
        pay_type: 2 //支付方式，1->线上支付，2->积分
      };

      app.get('ApiSweepstakes/getSet', {}, function (res) {
        if (res.status == 0) {
          app.alert(res.msg);
          return;
        }
        that.isGameOpen = !!res.is_open;
        that.getSweepstakesList();
      });
    },
    /**
     * 抽奖列表
     */
    getSweepstakesList: function getSweepstakesList() {
      var that = this;
      that.loading = true;
      app.get('ApiSweepstakes/getList', {}, /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(res) {
          return _regenerator.default.wrap(function _callee$(_context) {
            while (1) {
              switch (_context.prev = _context.next) {
                case 0:
                  that.loading = false;
                  if (!(res.status == 0)) {
                    _context.next = 4;
                    break;
                  }
                  app.alert(res.msg);
                  return _context.abrupt("return");
                case 4:
                  that.gameList = res.list;
                  if (that.isGameOpen && that.gameList && that.gameList.length > 0) {
                    that.currentGameInfo = that.gameList.find(function (item) {
                      return item.keyName === 'lucky';
                    });
                    that.luckyPrice = that.currentGameInfo.amount;
                    that.luckyPriceArray = [];
                    that.luckyPriceArray = Array.from({
                      length: 33
                    }, function (_, i) {
                      return (i + 8) / 10;
                    });
                    that.$refs.popupLucky.open();
                  }
                case 6:
                case "end":
                  return _context.stop();
              }
            }
          }, _callee);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }());
    },
    goEdit: function goEdit() {
      uni.navigateTo({
        url: '/daihuobiji/detail/fatie?goodid=' + this.opt.id
      });
    },
    savePhoto: function savePhoto() {
      var that = this;
      // 首先下载图片
      uni.downloadFile({
        url: that.posterpic,
        success: function success(downloadResult) {
          if (downloadResult.statusCode === 200) {
            // 下载成功，保存图片到系统相册
            uni.saveImageToPhotosAlbum({
              filePath: downloadResult.tempFilePath,
              success: function success() {
                uni.showToast({
                  title: '图片保存成功'
                });
              },
              fail: function fail() {
                uni.showToast({
                  title: '图片保存失败',
                  icon: 'none'
                });
              }
            });
          } else {
            uni.showToast({
              title: '图片下载失败',
              icon: 'none'
            });
          }
        },
        fail: function fail() {
          uni.showToast({
            title: '图片下载失败',
            icon: 'none'
          });
        }
      });
    },
    showLinkChange: function showLinkChange() {
      this.showLinkStatus = !this.showLinkStatus;
    },
    getdata: function getdata() {
      var that = this;
      var id = this.opt.id || 0;
      that.loading = true;
      app.get('ApiShop/product', {
        id: id
      }, function (res) {
        console.log(res);
        that.loading = false;
        if (res.status == 0) {
          app.alert(res.msg);
          return;
        }
        that.textset = app.globalData.textset;
        var product = res.product;
        var pagecontent = JSON.parse(product.detail);
        that.business = res.business;
        that.product = product;
        that.cartnum = res.cartnum;
        that.commentlist = res.commentlist;
        that.commentcount = res.commentcount;
        that.cuxiaolist = res.cuxiaolist;
        that.couponlist = res.couponlist;
        that.fuwulist = res.fuwulist;
        that.notes = product.notes || [];
        that.notes_total = product.notes_total || 0;
        that.pagecontent = pagecontent;
        that.shopset = res.shopset;
        that.sysset = res.sysset;
        that.title = product.name;
        that.isfavorite = res.isfavorite;
        that.showjiesheng = res.showjiesheng || 0;
        that.tjdatalist = res.tjdatalist || [];
        that.showtoptabbar = res.showtoptabbar || 0;
        that.bboglist = res.bboglist;
        that.sharepic = product.pics[0];
        that.xcx_scheme = res.xcx_scheme;
        // 处理自定义菜单数据
        if (res.shopdetail_menudata) {
          that.shopdetail_menudataList = res.shopdetail_menudata.list;
          that.bottomImg = res.shopdetail_menudata.bottomImg;
        } else {
          that.shopdetail_menudataList = false;
          that.bottomImg = '';
        }
        uni.setNavigationBarTitle({
          title: product.name
        });
        that.kfurl = '/pagesExt/kefu/index?bid=' + product.bid;
        if (app.globalData.initdata.kfurl != '') {
          that.kfurl = app.globalData.initdata.kfurl;
        }
        if (that.business && that.business.kfurl) {
          that.kfurl = that.business.kfurl;
        }
        that.loaded({
          title: product.sharetitle || product.name,
          pic: product.sharepic || product.pic,
          desc: product.sharedesc || product.sellpoint
        });
        setTimeout(function () {
          var view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0');
          view0.fields({
            size: true,
            //是否返回节点尺寸（width height）
            rect: false,
            //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
            scrollOffset: false //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
          }, function (res) {
            console.log(res);
            that.scrolltab0Height = res.height;
          }).exec();
          var view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1');
          view1.fields({
            size: true,
            //是否返回节点尺寸（width height）
            rect: false,
            //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
            scrollOffset: false //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
          }, function (res) {
            console.log(res);
            that.scrolltab1Height = res.height;
          }).exec();
          var view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2');
          view2.fields({
            size: true,
            //是否返回节点尺寸（width height）
            rect: false,
            //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
            scrollOffset: false //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
          }, function (res) {
            console.log(res);
            that.scrolltab2Height = res.height;
          }).exec();
        }, 500);
      });
    },
    swiperChange: function swiperChange(e) {
      var that = this;
      that.current = e.detail.current;
    },
    payvideo: function payvideo() {
      this.isplay = 1;
      uni.createVideoContext('video').play();
    },
    parsevideo: function parsevideo() {
      this.isplay = 0;
      uni.createVideoContext('video').stop();
    },
    buydialogChange: function buydialogChange(e, btnType) {
      if (!this.buydialogShow) {
        // 如果是直接传入btnType参数的调用方式
        if (btnType) {
          this.btntype = btnType;
        }
        // 如果是事件触发的调用方式
        else if (e && e.currentTarget && e.currentTarget.dataset) {
          this.btntype = e.currentTarget.dataset.btntype;
        }
      }
      this.buydialogShow = !this.buydialogShow;
    },
    //收藏操作
    addfavorite: function addfavorite() {
      var that = this;
      var proid = that.product.id;
      app.post('ApiShop/addfavorite', {
        proid: proid,
        type: 'shop'
      }, function (data) {
        if (data.status == 1) {
          that.isfavorite = !that.isfavorite;
        }
        app.success(data.msg);
      });
    },
    shareClick: function shareClick() {
      this.sharetypevisible = true;
    },
    handleClickMask: function handleClickMask() {
      this.sharetypevisible = false;
    },
    showPoster: function showPoster() {
      var that = this;
      that.showposter = true;
      that.sharetypevisible = false;
      app.showLoading('生成海报中');
      app.post('ApiShop/getposter', {
        proid: that.product.id
      }, function (data) {
        app.showLoading(false);
        if (data.status == 0) {
          app.alert(data.msg);
        } else {
          that.posterpic = data.poster;
        }
      });
    },
    shareScheme: function shareScheme() {
      var that = this;
      app.showLoading();
      app.post('ApiShop/getwxScheme', {
        proid: that.product.id
      }, function (data) {
        app.showLoading(false);
        if (data.status == 0) {
          app.alert(data.msg);
        } else {
          that.showScheme = true;
          that.schemeurl = data.openlink;
        }
      });
    },
    schemeDialogClose: function schemeDialogClose() {
      this.showScheme = false;
    },
    posterDialogClose: function posterDialogClose() {
      this.showposter = false;
    },
    showfuwudetail: function showfuwudetail() {
      this.showfuwudialog = true;
    },
    hidefuwudetail: function hidefuwudetail() {
      this.showfuwudialog = false;
    },
    showcuxiaodetail: function showcuxiaodetail() {
      this.showcuxiaodialog = true;
    },
    hidecuxiaodetail: function hidecuxiaodetail() {
      this.showcuxiaodialog = false;
    },
    getcoupon: function getcoupon() {
      this.showcuxiaodialog = false;
      this.getdata();
    },
    onPageScroll: function onPageScroll(e) {
      //var that = this;
      //var scrollY = e.scrollTop;     
      //if (scrollY > 200) {
      //	that.scrolltopshow = true;
      //}
      //if(scrollY < 150) {
      //	that.scrolltopshow = false
      //}
      //if (scrollY > 100) {
      //	that.toptabbar_show = true;
      //}
      //if(scrollY < 50) {
      //	that.toptabbar_show = false
      //}
    },
    changetoptab: function changetoptab(e) {
      var index = e.currentTarget.dataset.index;
      this.scrollToViewId = 'scroll_view_tab' + index;
      this.toptabbar_index = index;
      if (index == 0) this.scrollTop = 0;
      console.log(index);
    },
    scroll: function scroll(e) {
      var scrollTop = e.detail.scrollTop;
      //console.log(e)
      var that = this;
      if (scrollTop > 200) {
        that.scrolltopshow = true;
      }
      if (scrollTop < 150) {
        that.scrolltopshow = false;
      }
      if (scrollTop > 100) {
        that.toptabbar_show = true;
      }
      if (scrollTop < 50) {
        that.toptabbar_show = false;
      }
      var height0 = that.scrolltab0Height;
      var height1 = that.scrolltab0Height + that.scrolltab1Height;
      var height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;
      //var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;
      console.log('-----------------------');
      console.log(scrollTop);
      console.log(height2);
      if (scrollTop >= 0 && scrollTop < height0) {
        //this.scrollToViewId = 'scroll_view_tab0';
        this.toptabbar_index = 0;
      } else if (scrollTop >= height0 && scrollTop < height1) {
        //this.scrollToViewId = 'scroll_view_tab1';
        this.toptabbar_index = 1;
      } else if (scrollTop >= height1 && scrollTop < height2) {
        //this.scrollToViewId = 'scroll_view_tab2';
        this.toptabbar_index = 2;
      } else if (scrollTop >= height2) {
        //this.scrollToViewId = 'scroll_view_tab3';
        this.toptabbar_index = 3;
      }
    },
    sharemp: function sharemp() {
      app.error('点击右上角发送给好友或分享到朋友圈');
      this.sharetypevisible = false;
    },
    shareapp: function shareapp() {
      var that = this;
      that.sharetypevisible = false;
      uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function success(res) {
          if (res.tapIndex >= 0) {
            var scene = 'WXSceneSession';
            if (res.tapIndex == 1) {
              scene = 'WXSenceTimeline';
            }
            var sharedata = {};
            sharedata.provider = 'weixin';
            sharedata.type = 0;
            sharedata.scene = scene;
            sharedata.title = that.product.sharetitle || that.product.name;
            sharedata.summary = that.product.sharedesc || that.product.sellpoint;
            sharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/shopPackage/shop/product?scene=id_' + that.product.id + '-pid_' + app.globalData.mid;
            sharedata.imageUrl = that.product.pic;
            var sharelist = app.globalData.initdata.sharelist;
            if (sharelist) {
              for (var i = 0; i < sharelist.length; i++) {
                if (sharelist[i]['indexurl'] == '/shopPackage/shop/product') {
                  sharedata.title = sharelist[i].title;
                  sharedata.summary = sharelist[i].desc;
                  sharedata.imageUrl = sharelist[i].pic;
                  if (sharelist[i].url) {
                    var sharelink = sharelist[i].url;
                    if (sharelink.indexOf('/') === 0) {
                      sharelink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#' + sharelink;
                    }
                    if (app.globalData.mid > 0) {
                      sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid=' + app.globalData.mid;
                    }
                    sharedata.href = sharelink;
                  }
                }
              }
            }
            uni.share(sharedata);
          }
        }
      });
    },
    showsubqrcode: function showsubqrcode() {
      this.$refs.qrcodeDialog.open();
    },
    closesubqrcode: function closesubqrcode() {
      this.$refs.qrcodeDialog.close();
    },
    addcart: function addcart(e) {
      console.log(e);
      this.cartnum = this.cartnum + e.num;
    },
    showgg1Dialog: function showgg1Dialog() {
      this.$refs.gg1Dialog.open();
    },
    closegg1Dialog: function closegg1Dialog() {
      this.$refs.gg1Dialog.close();
    },
    showgg2Dialog: function showgg2Dialog() {
      this.$refs.gg2Dialog.open();
    },
    closegg2Dialog: function closegg2Dialog() {
      this.$refs.gg2Dialog.close();
    },
    /**
     * 点击游戏
     * @param {Object} item
     */
    onClickGame: function onClickGame(item) {
      var keyName = item.keyName,
        path = item.path;
      this.currentGameInfo = Object.assign({}, item);
      switch (keyName) {
        case 'lucky':
          this.luckyPrice = item.amount;
          this.$refs.popupLucky.open('top');
          break;
        default:
          app.goto("/game/".concat(keyName, "?id=").concat(this.opt.id, "&gameId=").concat(item.id, "&amount=").concat(item.amount));
          break;
      }
    },
    /**
     * 输入幸运价
     */
    inputLuckyPrice: function inputLuckyPrice(e) {
      var value = e.detail.value;
      this.luckyPrice = value;
    },
    /**
     * 中奖概率
     */
    getProbability: function getProbability() {
      var num = this.luckyPrice / this.product.min_price;
      return (num && num * 100 || 0).toFixed(2);
    },
    onCloseLucky: function onCloseLucky() {
      this.currentGameInfo = {};
      this.$refs.popupLucky.close();
    },
    /**
     * 幸运夺宝-修改价格
     */
    pickerChangePrice: function pickerChangePrice(e) {
      this.luckyPrice = this.luckyPriceArray[e.detail.value];
    },
    /**
     * 立即夺宝
     */
    onLuckyNow: function onLuckyNow() {
      this.$refs.popupLucky.close();
      app.goto("/game/lucky?id=".concat(this.opt.id, "&gameId=").concat(this.currentGameInfo.id, "&amount=").concat(this.luckyPrice));
    },
    hidePriceLink: function hidePriceLink() {
      app.goto(this.product.hide_price_link);
    },
    // 添加自定义链接跳转方法
    goToCustomLink: function goToCustomLink(type) {
      if (type === 'buybtn' && this.product.buybtn_link_url) {
        this.goto({
          currentTarget: {
            dataset: {
              url: this.product.buybtn_link_url
            }
          }
        });
      } else if (type === 'addcart' && this.product.addcart_link_url) {
        this.goto({
          currentTarget: {
            dataset: {
              url: this.product.addcart_link_url
            }
          }
        });
      }
    },
    // 处理自定义菜单点击
    addfavorite2: function addfavorite2(item) {
      var that = this;
      if (item.pagePath == 'addfavorite::') {
        var proid = that.product.id;
        app.post('ApiShop/addfavorite', {
          proid: proid,
          type: 'shop'
        }, function (data) {
          if (data.status == 1) {
            that.isfavorite = !that.isfavorite;
          }
          app.success(data.msg);
        });
      } else {
        var url = '';
        if (item.menuType == 2) {
          if (item.pagePath) {
            url = item.pagePath;
          } else {
            url = '/pages/shop/cart';
          }
        }
        if (item.menuType == 1) {
          if (item.pagePath) {
            url = item.pagePath;
          } else {
            url = 'pages/kefu/index';
          }
        }
        if (item.menuType == 3 || item.menuType == 4) {
          url = item.pagePath;
        }
        // 判断是否为基础页面
        if (url == '/pages/shop/cart') return app.goto(url);
        if (url == '/pages/my/usercenter') return app.goto(url);
        if (url == '/pages/shop/classify') return app.goto(url);
        if (url == '/pages/shop/prolist') return app.goto(url);
        if (url == '/pages/index/index') return app.goto(url);
        if (url.split('?')[1] && url.split('?')[1].split('=')[0] == 'bid') {
          app.goto(url);
        } else {
          if (url.indexOf('tel:') === 0) {
            app.goto(url);
            return;
          }
          app.goto(url + '?bid=' + that.product.bid);
        }
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 155:
/*!***********************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?vue&type=style&index=0&lang=css& ***!
  \***********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css& */ 156);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_product_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 156:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?vue&type=style&index=0&lang=css& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[146,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/shopPackage/shop/product.js.map