(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["shopPackage/shop/productcopy"],{

/***/ 245:
/*!*************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/main.js?{"page":"shopPackage%2Fshop%2Fproductcopy"} ***!
  \*************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _productcopy = _interopRequireDefault(__webpack_require__(/*! ./shopPackage/shop/productcopy.vue */ 246));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_productcopy.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 246:
/*!******************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/productcopy.vue ***!
  \******************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./productcopy.vue?vue&type=template&id=4e99d6d0& */ 247);
/* harmony import */ var _productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./productcopy.vue?vue&type=script&lang=js& */ 249);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _productcopy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./productcopy.vue?vue&type=style&index=0&lang=css& */ 251);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 34);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__["render"],
  _productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "shopPackage/shop/productcopy.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 247:
/*!*************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/productcopy.vue?vue&type=template&id=4e99d6d0& ***!
  \*************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./productcopy.vue?vue&type=template&id=4e99d6d0& */ 248);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_template_id_4e99d6d0___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 248:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/productcopy.vue?vue&type=template&id=4e99d6d0& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return Promise.all(/*! import() | components/uni-popup/uni-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-popup/uni-popup")]).then(__webpack_require__.bind(null, /*! @/components/uni-popup/uni-popup.vue */ 7841))
    },
    dp: function () {
      return __webpack_require__.e(/*! import() | components/dp/dp */ "components/dp/dp").then(__webpack_require__.bind(null, /*! @/components/dp/dp.vue */ 7850))
    },
    dpProductItem: function () {
      return __webpack_require__.e(/*! import() | components/dp-product-item/dp-product-item */ "components/dp-product-item/dp-product-item").then(__webpack_require__.bind(null, /*! @/components/dp-product-item/dp-product-item.vue */ 7929))
    },
    buydialog: function () {
      return __webpack_require__.e(/*! import() | components/buydialog/buydialog */ "components/buydialog/buydialog").then(__webpack_require__.bind(null, /*! @/components/buydialog/buydialog.vue */ 7936))
    },
    loading: function () {
      return __webpack_require__.e(/*! import() | components/loading/loading */ "components/loading/loading").then(__webpack_require__.bind(null, /*! @/components/loading/loading.vue */ 5109))
    },
    dpTabbar: function () {
      return __webpack_require__.e(/*! import() | components/dp-tabbar/dp-tabbar */ "components/dp-tabbar/dp-tabbar").then(__webpack_require__.bind(null, /*! @/components/dp-tabbar/dp-tabbar.vue */ 5116))
    },
    popmsg: function () {
      return __webpack_require__.e(/*! import() | components/popmsg/popmsg */ "components/popmsg/popmsg").then(__webpack_require__.bind(null, /*! @/components/popmsg/popmsg.vue */ 5123))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t("color1") : null
  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t("color1") : null
  var g0 = _vm.isload ? _vm.bboglist.length : null
  var m2 =
    _vm.isload &&
    _vm.showtoptabbar == 1 &&
    _vm.toptabbar_show == 1 &&
    _vm.toptabbar_index == 0
      ? _vm.t("color1")
      : null
  var m3 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1
      ? _vm.t("color1")
      : null
  var m4 =
    _vm.isload &&
    _vm.showtoptabbar == 1 &&
    _vm.toptabbar_show == 1 &&
    _vm.toptabbar_index == 1
      ? _vm.t("color1")
      : null
  var m5 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1
      ? _vm.t("color1")
      : null
  var m6 =
    _vm.isload &&
    _vm.showtoptabbar == 1 &&
    _vm.toptabbar_show == 1 &&
    _vm.toptabbar_index == 2
      ? _vm.t("color1")
      : null
  var m7 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1
      ? _vm.t("color1")
      : null
  var g1 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1
      ? _vm.tjdatalist.length
      : null
  var m8 =
    _vm.isload &&
    _vm.showtoptabbar == 1 &&
    _vm.toptabbar_show == 1 &&
    g1 > 0 &&
    _vm.toptabbar_index == 3
      ? _vm.t("color1")
      : null
  var m9 =
    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1 && g1 > 0
      ? _vm.t("color1")
      : null
  var g2 = _vm.isload ? _vm.tjdatalist.length : null
  var m10 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    _vm.showjiesheng == 1
      ? _vm.t("color2")
      : null
  var m11 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    _vm.showjiesheng == 1
      ? _vm.t("color1")
      : null
  var m12 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    !(_vm.showjiesheng == 1) &&
    _vm.product.price_type == 1
      ? _vm.t("color1")
      : null
  var m13 =
    _vm.isload &&
    _vm.product.status == 1 &&
    !_vm.showcuxiaodialog &&
    !(_vm.showjiesheng == 1) &&
    !(_vm.product.price_type == 1)
      ? _vm.t("color1")
      : null
  var m14 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null
  var m15 =
    _vm.isload && _vm.sharetypevisible && !(m14 == "app")
      ? _vm.getplatform()
      : null
  var m16 =
    _vm.isload && _vm.sharetypevisible && !(m14 == "app") && !(m15 == "mp")
      ? _vm.getplatform()
      : null
  var m17 =
    _vm.isload && _vm.sharetypevisible
      ? _vm.getplatform() == "wx" && _vm.xcx_scheme
      : null
  var m18 = _vm.isload && _vm.showScheme ? _vm.t("color1") : null
  var m19 = _vm.isload && _vm.showScheme ? _vm.t("color1rgb") : null
  var m20 =
    _vm.isload && _vm.showLinkStatus && _vm.business.tel
      ? _vm.t("color1")
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
        m6: m6,
        m7: m7,
        g1: g1,
        m8: m8,
        m9: m9,
        g2: g2,
        m10: m10,
        m11: m11,
        m12: m12,
        m13: m13,
        m14: m14,
        m15: m15,
        m16: m16,
        m17: m17,
        m18: m18,
        m19: m19,
        m20: m20,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 249:
/*!*******************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/productcopy.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./productcopy.vue?vue&type=script&lang=js& */ 250);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 250:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/productcopy.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var interval = null;
var _default = {
  data: function data() {
    var _ref;
    return _ref = {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      textset: {}
    }, (0, _defineProperty2.default)(_ref, "isload", false), (0, _defineProperty2.default)(_ref, "buydialogShow", false), (0, _defineProperty2.default)(_ref, "btntype", 1), (0, _defineProperty2.default)(_ref, "isfavorite", false), (0, _defineProperty2.default)(_ref, "current", 0), (0, _defineProperty2.default)(_ref, "isplay", 0), (0, _defineProperty2.default)(_ref, "showcuxiaodialog", false), (0, _defineProperty2.default)(_ref, "showfuwudialog", false), (0, _defineProperty2.default)(_ref, "business", ""), (0, _defineProperty2.default)(_ref, "product", []), (0, _defineProperty2.default)(_ref, "cartnum", ""), (0, _defineProperty2.default)(_ref, "commentlist", ""), (0, _defineProperty2.default)(_ref, "commentcount", ""), (0, _defineProperty2.default)(_ref, "cuxiaolist", ""), (0, _defineProperty2.default)(_ref, "couponlist", ""), (0, _defineProperty2.default)(_ref, "fuwulist", []), (0, _defineProperty2.default)(_ref, "pagecontent", ""), (0, _defineProperty2.default)(_ref, "shopset", {}), (0, _defineProperty2.default)(_ref, "sysset", {}), (0, _defineProperty2.default)(_ref, "title", ""), (0, _defineProperty2.default)(_ref, "bboglist", ""), (0, _defineProperty2.default)(_ref, "sharepic", ""), (0, _defineProperty2.default)(_ref, "sharetypevisible", false), (0, _defineProperty2.default)(_ref, "showposter", false), (0, _defineProperty2.default)(_ref, "posterpic", ""), (0, _defineProperty2.default)(_ref, "scrolltopshow", false), (0, _defineProperty2.default)(_ref, "kfurl", ''), (0, _defineProperty2.default)(_ref, "showLinkStatus", false), (0, _defineProperty2.default)(_ref, "showjiesheng", 0), (0, _defineProperty2.default)(_ref, "tjdatalist", []), (0, _defineProperty2.default)(_ref, "showtoptabbar", 0), (0, _defineProperty2.default)(_ref, "toptabbar_show", 0), (0, _defineProperty2.default)(_ref, "toptabbar_index", 0), (0, _defineProperty2.default)(_ref, "scrollToViewId", ""), (0, _defineProperty2.default)(_ref, "scrollTop", 0), (0, _defineProperty2.default)(_ref, "scrolltab0Height", 0), (0, _defineProperty2.default)(_ref, "scrolltab1Height", 0), (0, _defineProperty2.default)(_ref, "scrolltab2Height", 0), (0, _defineProperty2.default)(_ref, "scrolltab3Height", 0), (0, _defineProperty2.default)(_ref, "xcx_scheme", false), (0, _defineProperty2.default)(_ref, "showScheme", false), (0, _defineProperty2.default)(_ref, "schemeurl", ''), _ref;
  },
  onLoad: function onLoad(opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  onShow: function onShow(e) {
    uni.$emit('getglassrecord');
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.getdata();
  },
  onShareAppMessage: function onShareAppMessage() {
    return this._sharewx({
      title: this.product.sharetitle || this.product.name,
      pic: this.product.sharepic || this.product.pic
    });
  },
  onShareTimeline: function onShareTimeline() {
    var sharewxdata = this._sharewx({
      title: this.product.sharetitle || this.product.name,
      pic: this.product.sharepic || this.product.pic
    });
    var query = sharewxdata.path.split('?')[1];
    console.log(sharewxdata);
    console.log(query);
    return {
      title: sharewxdata.title,
      imageUrl: sharewxdata.imageUrl,
      query: query
    };
  },
  onUnload: function onUnload() {
    clearInterval(interval);
  },
  methods: {
    showLinkChange: function showLinkChange() {
      this.showLinkStatus = !this.showLinkStatus;
    },
    getdata: function getdata() {
      var that = this;
      var id = this.opt.id || 0;
      that.loading = true;
      app.get('ApiShop/product', {
        id: id
      }, function (res) {
        that.loading = false;
        if (res.status == 0) {
          app.alert(res.msg);
          return;
        }
        that.textset = app.globalData.textset;
        var product = res.product;
        var pagecontent = JSON.parse(product.detail);
        that.business = res.business;
        that.product = product;
        that.cartnum = res.cartnum;
        that.commentlist = res.commentlist;
        that.commentcount = res.commentcount;
        that.cuxiaolist = res.cuxiaolist;
        that.couponlist = res.couponlist;
        that.fuwulist = res.fuwulist;
        that.pagecontent = pagecontent;
        that.shopset = res.shopset;
        that.sysset = res.sysset;
        that.title = product.name;
        that.isfavorite = res.isfavorite;
        that.showjiesheng = res.showjiesheng || 0;
        that.tjdatalist = res.tjdatalist || [];
        that.showtoptabbar = res.showtoptabbar || 0;
        that.bboglist = res.bboglist;
        that.sharepic = product.pics[0];
        that.xcx_scheme = res.xcx_scheme;
        uni.setNavigationBarTitle({
          title: product.name
        });
        that.kfurl = '/pagesExt/kefu/index?bid=' + product.bid;
        if (app.globalData.initdata.kfurl != '') {
          that.kfurl = app.globalData.initdata.kfurl;
        }
        if (that.business && that.business.kfurl) {
          that.kfurl = that.business.kfurl;
        }
        that.loaded({
          title: product.sharetitle || product.name,
          pic: product.sharepic || product.pic,
          desc: product.sharedesc || product.sellpoint
        });
        setTimeout(function () {
          var view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0');
          view0.fields({
            size: true,
            //是否返回节点尺寸（width height）
            rect: false,
            //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
            scrollOffset: false //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
          }, function (res) {
            console.log(res);
            that.scrolltab0Height = res.height;
          }).exec();
          var view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1');
          view1.fields({
            size: true,
            //是否返回节点尺寸（width height）
            rect: false,
            //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
            scrollOffset: false //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
          }, function (res) {
            console.log(res);
            that.scrolltab1Height = res.height;
          }).exec();
          var view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2');
          view2.fields({
            size: true,
            //是否返回节点尺寸（width height）
            rect: false,
            //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
            scrollOffset: false //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
          }, function (res) {
            console.log(res);
            that.scrolltab2Height = res.height;
          }).exec();
        }, 500);
      });
    },
    swiperChange: function swiperChange(e) {
      var that = this;
      that.current = e.detail.current;
    },
    payvideo: function payvideo() {
      this.isplay = 1;
      uni.createVideoContext('video').play();
    },
    parsevideo: function parsevideo() {
      this.isplay = 0;
      uni.createVideoContext('video').stop();
    },
    buydialogChange: function buydialogChange(e) {
      if (!this.buydialogShow) {
        this.btntype = e.currentTarget.dataset.btntype;
      }
      this.buydialogShow = !this.buydialogShow;
    },
    //收藏操作
    addfavorite: function addfavorite() {
      var that = this;
      var proid = that.product.id;
      app.post('ApiShop/addfavorite', {
        proid: proid,
        type: 'shop'
      }, function (data) {
        if (data.status == 1) {
          that.isfavorite = !that.isfavorite;
        }
        app.success(data.msg);
      });
    },
    shareClick: function shareClick() {
      this.sharetypevisible = true;
    },
    handleClickMask: function handleClickMask() {
      this.sharetypevisible = false;
    },
    showPoster: function showPoster() {
      var that = this;
      that.showposter = true;
      that.sharetypevisible = false;
      app.showLoading('生成海报中');
      app.post('ApiShop/getposter', {
        proid: that.product.id
      }, function (data) {
        app.showLoading(false);
        if (data.status == 0) {
          app.alert(data.msg);
        } else {
          that.posterpic = data.poster;
        }
      });
    },
    shareScheme: function shareScheme() {
      var that = this;
      app.showLoading();
      app.post('ApiShop/getwxScheme', {
        proid: that.product.id
      }, function (data) {
        app.showLoading(false);
        if (data.status == 0) {
          app.alert(data.msg);
        } else {
          that.showScheme = true;
          that.schemeurl = data.openlink;
        }
      });
    },
    schemeDialogClose: function schemeDialogClose() {
      this.showScheme = false;
    },
    posterDialogClose: function posterDialogClose() {
      this.showposter = false;
    },
    showfuwudetail: function showfuwudetail() {
      this.showfuwudialog = true;
    },
    hidefuwudetail: function hidefuwudetail() {
      this.showfuwudialog = false;
    },
    showcuxiaodetail: function showcuxiaodetail() {
      this.showcuxiaodialog = true;
    },
    hidecuxiaodetail: function hidecuxiaodetail() {
      this.showcuxiaodialog = false;
    },
    getcoupon: function getcoupon() {
      this.showcuxiaodialog = false;
      this.getdata();
    },
    onPageScroll: function onPageScroll(e) {
      //var that = this;
      //var scrollY = e.scrollTop;     
      //if (scrollY > 200) {
      //	that.scrolltopshow = true;
      //}
      //if(scrollY < 150) {
      //	that.scrolltopshow = false
      //}
      //if (scrollY > 100) {
      //	that.toptabbar_show = true;
      //}
      //if(scrollY < 50) {
      //	that.toptabbar_show = false
      //}
    },
    changetoptab: function changetoptab(e) {
      var index = e.currentTarget.dataset.index;
      this.scrollToViewId = 'scroll_view_tab' + index;
      this.toptabbar_index = index;
      if (index == 0) this.scrollTop = 0;
      console.log(index);
    },
    scroll: function scroll(e) {
      var scrollTop = e.detail.scrollTop;
      //console.log(e)
      var that = this;
      if (scrollTop > 200) {
        that.scrolltopshow = true;
      }
      if (scrollTop < 150) {
        that.scrolltopshow = false;
      }
      if (scrollTop > 100) {
        that.toptabbar_show = true;
      }
      if (scrollTop < 50) {
        that.toptabbar_show = false;
      }
      var height0 = that.scrolltab0Height;
      var height1 = that.scrolltab0Height + that.scrolltab1Height;
      var height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;
      //var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;
      console.log('-----------------------');
      console.log(scrollTop);
      console.log(height2);
      if (scrollTop >= 0 && scrollTop < height0) {
        //this.scrollToViewId = 'scroll_view_tab0';
        this.toptabbar_index = 0;
      } else if (scrollTop >= height0 && scrollTop < height1) {
        //this.scrollToViewId = 'scroll_view_tab1';
        this.toptabbar_index = 1;
      } else if (scrollTop >= height1 && scrollTop < height2) {
        //this.scrollToViewId = 'scroll_view_tab2';
        this.toptabbar_index = 2;
      } else if (scrollTop >= height2) {
        //this.scrollToViewId = 'scroll_view_tab3';
        this.toptabbar_index = 3;
      }
    },
    sharemp: function sharemp() {
      app.error('点击右上角发送给好友或分享到朋友圈');
      this.sharetypevisible = false;
    },
    shareapp: function shareapp() {
      var that = this;
      that.sharetypevisible = false;
      uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function success(res) {
          if (res.tapIndex >= 0) {
            var scene = 'WXSceneSession';
            if (res.tapIndex == 1) {
              scene = 'WXSenceTimeline';
            }
            var sharedata = {};
            sharedata.provider = 'weixin';
            sharedata.type = 0;
            sharedata.scene = scene;
            sharedata.title = that.product.sharetitle || that.product.name;
            sharedata.summary = that.product.sharedesc || that.product.sellpoint;
            sharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/shopPackage/shop/product?scene=id_' + that.product.id + '-pid_' + app.globalData.mid;
            sharedata.imageUrl = that.product.pic;
            var sharelist = app.globalData.initdata.sharelist;
            if (sharelist) {
              for (var i = 0; i < sharelist.length; i++) {
                if (sharelist[i]['indexurl'] == '/shopPackage/shop/product') {
                  sharedata.title = sharelist[i].title;
                  sharedata.summary = sharelist[i].desc;
                  sharedata.imageUrl = sharelist[i].pic;
                  if (sharelist[i].url) {
                    var sharelink = sharelist[i].url;
                    if (sharelink.indexOf('/') === 0) {
                      sharelink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#' + sharelink;
                    }
                    if (app.globalData.mid > 0) {
                      sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid=' + app.globalData.mid;
                    }
                    sharedata.href = sharelink;
                  }
                }
              }
            }
            uni.share(sharedata);
          }
        }
      });
    },
    showsubqrcode: function showsubqrcode() {
      this.$refs.qrcodeDialog.open();
    },
    closesubqrcode: function closesubqrcode() {
      this.$refs.qrcodeDialog.close();
    },
    addcart: function addcart(e) {
      console.log(e);
      this.cartnum = this.cartnum + e.num;
    },
    showgg1Dialog: function showgg1Dialog() {
      this.$refs.gg1Dialog.open();
    },
    closegg1Dialog: function closegg1Dialog() {
      this.$refs.gg1Dialog.close();
    },
    showgg2Dialog: function showgg2Dialog() {
      this.$refs.gg2Dialog.open();
    },
    closegg2Dialog: function closegg2Dialog() {
      this.$refs.gg2Dialog.close();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 251:
/*!***************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/productcopy.vue?vue&type=style&index=0&lang=css& ***!
  \***************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./productcopy.vue?vue&type=style&index=0&lang=css& */ 252);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_productcopy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 252:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/productcopy.vue?vue&type=style&index=0&lang=css& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[245,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/shopPackage/shop/productcopy.js.map